@echo off
cd /d "%~dp0"
title MQS Enhanced Aimbot

cls
echo ================================================================
echo MQS Enhanced Aimbot - Real Color Detection
echo ================================================================
echo.

echo Checking Python...
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Python not found
    echo Install Python from python.org
    pause
    exit
)
echo [OK] Python found

echo.
echo Checking files...
if not exist "enhanced_aimbot.py" (
    echo [ERROR] enhanced_aimbot.py not found
    echo Make sure you are in the correct directory
    pause
    exit
)
echo [OK] enhanced_aimbot.py found

echo.
echo Starting Enhanced Aimbot...
echo.
echo Features:
echo - Ultra Strong Tracking (1000)
echo - Enhanced Lock System (600)
echo - Real Color Detection (400)
echo - Smooth Mouse Movement
echo - Live Statistics
echo.
echo Controls:
echo - ALT = Toggle Aimbot
echo - INS = Stop Script
echo.
echo Note: For best results, run as administrator
echo.

python enhanced_aimbot.py

echo.
echo Program finished
pause
