@echo off
chcp 65001 >nul 2>&1
title MQS Ultra Tracking Aimbot

echo ================================================================
echo MQS Ultra Tracking Aimbot for Overwatch 2
echo Advanced Ultra Tracking System
echo ================================================================
echo.

echo New Features:
echo - Ultra Tracking 600%% for moving enemies
echo - Super Lock during shooting
echo - Enhanced color detection with 72 specific colors
echo - Enemy movement prediction
echo - Advanced recoil compensation
echo - Natural movement with randomness
echo.

echo Requesting Administrator privileges...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Administrator privileges granted
) else (
    echo [ERROR] Administrator privileges required
    echo Right-click and select "Run as administrator"
    pause
    exit
)

echo.
echo Checking Python installation...
python --version >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Python found
) else (
    echo [ERROR] Python not installed
    echo Please install Python from python.org
    pause
    exit
)

echo.
echo Checking Overwatch 2...
tasklist /FI "IMAGENAME eq Overwatch.exe" 2>NUL | find /I /N "Overwatch.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [OK] Overwatch 2 found
) else (
    echo [WARNING] Overwatch 2 not found
    echo Make sure the game is running first
    echo.
    set /p choice="Continue anyway? (y/n): "
    if /i "%choice%" neq "y" exit
)

echo.
echo Starting Ultra Tracking System...
echo.
echo Usage Instructions:
echo - ALT = Enable/Disable Ultra Tracking
echo - INS = Stop script completely
echo - Adjust power from interface
echo - Make sure game is in foreground
echo.
echo Ultra Tracking Settings:
echo - Tracking Power: 600%%
echo - Lock Strength: 400%%
echo - Color Detection: 400%%
echo - Movement Prediction: Active
echo - Aggressive Tracking: Active
echo.

python run_real_aimbot.py

echo.
echo Program finished
pause
