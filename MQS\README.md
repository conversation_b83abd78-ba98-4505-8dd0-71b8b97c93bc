# 🎯 MQS Project - Advanced AimBot System

## ✅ **Solutions for Your Problems**

### 💪 **Problem 1: Weak Aim Assist and Recoil Control**
**Solution:** **MQS Ultra Strong AimBot**
- Ultra Strong Aim Assist (100% Power)
- Ultra Strong AimBot (100% Power)
- Ultra Strong Recoil Control (100% Power)
- Maximum Strength - No Weakness!

### 🔥 **Problem 2: Bullets Don't Track Enemies While Shooting**
**Solution:** **MQS Fire Tracking AimBot**
- Tracks enemies WHILE you're shooting
- Corrects aim during continuous fire
- Real-time recoil compensation while firing
- Bullet magnetism during shooting

### 🌐 **Problem 3: Weak Color Detection & Need Arabic Interface (NEW!)**
**Solution:** **MQS Arabic Ultra Strong AimBot**
- 🌐 **Full Arabic Interface** (واجهة عربية كاملة)
- 🎨 **Enhanced Color Detection** for Red, Purple, Violet, Pink
- 🎯 **150% Power** (stronger than 100%)
- 🔥 **Strong Fire Tracking** while shooting
- ⬇️ **Enhanced Recoil Control**
- 💪 **Solves all weakness problems**

---

## 🚀 **How to Use**

### **Quick Start:**
1. Run `START.bat`
2. Choose your solution:
   - **[1]** Ultra Strong AimBot (for maximum power)
   - **[2]** Fire Tracking AimBot (for tracking while shooting)
   - **[3]** Arabic Ultra Strong AimBot (for Arabic interface & enhanced features)
3. Follow the instructions

### **Direct Start:**
- **Ultra Strong:** `ultra_strong.bat`
- **Fire Tracking:** `fire_tracking.bat`
- **Arabic Ultra Strong:** `arabic_ultra_strong.bat`

---

## 📁 **Files Structure**

```
MQS/
├── START.bat                        # Main menu
├── ultra_strong.bat                 # Ultra Strong AimBot launcher
├── fire_tracking.bat               # Fire Tracking AimBot launcher
├── arabic_ultra_strong.bat          # Arabic Ultra Strong launcher (NEW!)
├── mqs_ultra_strong_aimbot.py      # Ultra Strong system
├── mqs_fire_tracking_aimbot.py     # Fire Tracking system
├── mqs_arabic_ultra_strong.py      # Arabic Ultra Strong system (NEW!)
├── requirements.txt                # Required libraries
└── README.md                       # This file
```

---

## ⚙️ **Requirements**

The programs will automatically install required libraries:
- opencv-python
- mss
- pywin32
- psutil
- numpy

---

## 🎯 **Features**

### **Ultra Strong AimBot:**
- 100% Power Aim Assist
- 100% Power AimBot
- 100% Power Recoil Control
- Instant Target Snap
- Aggressive Tracking
- Maximum Correction

### **Fire Tracking AimBot:**
- Fire Tracking System
- Continuous Aim Correction
- Real-Time Recoil Compensation
- Fire Magnetism
- Burst Tracking
- Moving Target Tracking

### **🌐 Arabic Ultra Strong AimBot (NEW!):**
- **Arabic Interface:** Full Arabic GUI (واجهة عربية كاملة)
- **Enhanced Power:** 150% strength (أقوى من 100%)
- **Color Detection:** Enhanced detection for:
  - 🔴 Red (أحمر) - Multiple HSV ranges
  - 🟣 Purple (بنفسجي) - Enhanced detection
  - 🟪 Violet (أرجواني) - Improved recognition
  - 🩷 Pink (بنكي) - Strong detection
- **Fire Tracking:** 150% power while shooting
- **Recoil Control:** 150% compensation
- **Response Time:** Ultra-fast (0.0001s)
- **Target Prediction:** Advanced velocity calculation
- **Real-time Stats:** Live performance monitoring

---

## ⚠️ **Warning**

These are powerful tools. Use responsibly and at your own risk.

---

## 🎉 **Get Started**

Run `START.bat` and choose your solution!

### **للمستخدمين العرب (For Arabic Users):**
```bash
START.bat
# اختر الخيار رقم 3 للواجهة العربية القوية
# Choose option 3 for powerful Arabic interface
```

**🎯 اختر مستوى القوة وسيطر على اللعبة!**
