#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQS Aimbot Launcher
مشغل برامج التصويب
"""

import os
import sys
import subprocess

def main():
    print("=" * 60)
    print("🎯 MQS Aimbot Launcher")
    print("مشغل برامج التصويب")
    print("=" * 60)
    print()
    
    # التحقق من الملفات المتوفرة
    files = {
        '1': ('advanced_real_aimbot.py', '🎯 Advanced Real Aimbot - يعمل فعلاً مع اللعبة!'),
        '2': ('real_aimbot.py', '🎯 Basic Real Aimbot - يعمل فعلاً مع اللعبة!'),
        '3': ('enhanced_aimbot.py', '📊 Enhanced Aimbot - محاكي مع إحصائيات'),
        '4': ('simple_aimbot.py', '⚡ Simple Aimbot - محاكي بسيط'),
        '5': ('mqs_arabic_aimbot.py', '🔧 Original Aimbot - الإصدار الأصلي')
    }
    
    available_files = {}
    for key, (filename, description) in files.items():
        if os.path.exists(filename):
            available_files[key] = (filename, description)
    
    if not available_files:
        print("❌ لم يتم العثور على أي ملفات تصويب")
        input("اضغط Enter للخروج...")
        return
    
    print("📁 الملفات المتوفرة:")
    for key, (filename, description) in available_files.items():
        print(f"  {key}. {description}")
    
    print()
    print("0. خروج")
    print()
    
    while True:
        try:
            choice = input("اختر رقم البرنامج (1-3): ").strip()
            
            if choice == '0':
                print("👋 وداعاً!")
                break
            
            if choice in available_files:
                filename, description = available_files[choice]
                print(f"\n🚀 تشغيل {description}...")
                print("📋 التحكم:")
                print("• ALT = تفعيل/إلغاء التصويب")
                print("• INS = إيقاف السكربت")
                print("-" * 40)
                
                try:
                    subprocess.run([sys.executable, filename])
                except KeyboardInterrupt:
                    print("\n⏹️ تم إيقاف البرنامج")
                except Exception as e:
                    print(f"\n❌ خطأ في تشغيل البرنامج: {e}")
                
                print("\n" + "=" * 40)
                continue_choice = input("هل تريد تشغيل برنامج آخر؟ (y/n): ").strip().lower()
                if continue_choice not in ['y', 'yes', 'نعم']:
                    break
            else:
                print("❌ اختيار غير صحيح، حاول مرة أخرى")
                
        except KeyboardInterrupt:
            print("\n👋 وداعاً!")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
