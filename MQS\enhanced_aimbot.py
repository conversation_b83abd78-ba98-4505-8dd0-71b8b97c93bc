#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQS Enhanced Aimbot - Real Color Detection
نظام التصويب المحسن مع كشف الألوان الحقيقي
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import random
import math
import ctypes
import ctypes.wintypes
import sys
import os

# محاولة تحميل المكتبات المتقدمة
try:
    import keyboard
    import cv2
    import numpy as np
    import mss
    LIBS_AVAILABLE = True
    print("[✅] جميع المكتبات متوفرة - الوضع الحقيقي نشط")
except ImportError as e:
    LIBS_AVAILABLE = False
    print(f"[⚠️] مكتبات مفقودة: {e}")
    print("[ℹ️] سيعمل البرنامج في وضع المحاكاة")

class EnhancedAimbot:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 MQS Enhanced Aimbot - Real Color Detection")
        self.root.geometry("700x600")
        self.root.configure(bg='#0a0a0a')
        
        # متغيرات النظام
        self.system_active = False
        self.aimbot_enabled = False
        self.current_target = None
        
        # إعدادات القوة المحسنة
        self.settings = {
            'aim_strength': 1000,      # قوة فائقة
            'tracking_power': 600,     # تتبع قوي
            'lock_strength': 400,      # التصاق قوي
            'color_detection': 400,    # كشف ألوان قوي
            'sensitivity': 1.0,        # حساسية
            'max_move_pixels': 20,     # أقصى حركة
            'prediction_enabled': True  # تنبؤ بالحركة
        }
        
        # إعداد الشاشة
        self.screen_width = ctypes.windll.user32.GetSystemMetrics(0)
        self.screen_height = ctypes.windll.user32.GetSystemMetrics(1)
        
        # إعداد التقاط الشاشة
        self.screen_capture = None
        if LIBS_AVAILABLE:
            try:
                self.screen_capture = mss.mss()
                print("[✅] نظام التقاط الشاشة جاهز")
            except Exception as e:
                print(f"[⚠️] فشل تهيئة التقاط الشاشة: {e}")
        
        # ألوان Overwatch 2
        self.target_colors = [
            'd521cd', 'd722cf', 'd623ce', 'd722ce', 'd621cd', 'ce19ca',
            'c818cf', 'd722cd', 'cd19c9', 'c617d3', 'cb17c5', 'da25d3',
            'dc5bea', 'da59eb', 'd959e9', 'f444fb', 'cf1ac9', 'd422d4'
        ]
        
        # إحصائيات
        self.stats = {
            'targets_detected': 0,
            'locks_applied': 0,
            'colors_found': {'red': 0, 'purple': 0, 'pink': 0, 'violet': 0}
        }
        
        # إعداد زر INS
        self.setup_ins_key()
        
        # إنشاء الواجهة
        self.create_interface()
        
    def setup_ins_key(self):
        """إعداد زر INS لإيقاف السكربت"""
        if LIBS_AVAILABLE:
            try:
                keyboard.add_hotkey('insert', self.stop_script)
                print("[✅] تم تفعيل زر INS للإيقاف")
            except:
                print("[⚠️] فشل تفعيل زر INS")
    
    def create_interface(self):
        """إنشاء الواجهة المحسنة"""
        # العنوان
        title_frame = tk.Frame(self.root, bg='#0a0a0a')
        title_frame.pack(pady=10)
        
        title_label = tk.Label(title_frame, text="🎯 MQS Enhanced Aimbot", 
                              font=('Arial', 18, 'bold'), 
                              fg='#00ff41', bg='#0a0a0a')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame, text="نظام التصويب المحسن مع كشف الألوان الحقيقي", 
                                 font=('Arial', 10), 
                                 fg='#ffffff', bg='#0a0a0a')
        subtitle_label.pack()
        
        # أزرار التحكم
        control_frame = tk.Frame(self.root, bg='#0a0a0a')
        control_frame.pack(pady=15)
        
        self.start_btn = tk.Button(control_frame, text="🚀 تشغيل النظام", 
                                  command=self.start_system,
                                  font=('Arial', 12, 'bold'),
                                  bg='#00aa00', fg='white',
                                  width=12, height=2)
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = tk.Button(control_frame, text="⏹️ إيقاف", 
                                 command=self.stop_system,
                                 font=('Arial', 12, 'bold'),
                                 bg='#aa0000', fg='white',
                                 width=12, height=2)
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        # إعدادات القوة
        settings_frame = tk.LabelFrame(self.root, text="⚙️ إعدادات القوة المحسنة", 
                                      font=('Arial', 11, 'bold'),
                                      fg='#00ff41', bg='#0a0a0a')
        settings_frame.pack(pady=15, padx=20, fill='x')
        
        # شرائح التحكم
        self.create_slider(settings_frame, "قوة التصويب", 'aim_strength', 0, 2000, 1000)
        self.create_slider(settings_frame, "قوة التتبع", 'tracking_power', 0, 1000, 600)
        self.create_slider(settings_frame, "قوة الالتصاق", 'lock_strength', 0, 500, 400)
        self.create_slider(settings_frame, "كشف الألوان", 'color_detection', 0, 500, 400)
        
        # حالة النظام
        status_frame = tk.Frame(self.root, bg='#0a0a0a')
        status_frame.pack(pady=10)
        
        self.status_label = tk.Label(status_frame, text="🔴 النظام متوقف", 
                                    font=('Arial', 12, 'bold'),
                                    fg='#ff0000', bg='#0a0a0a')
        self.status_label.pack()
        
        # إحصائيات
        stats_frame = tk.LabelFrame(self.root, text="📊 الإحصائيات", 
                                   font=('Arial', 10, 'bold'),
                                   fg='#00ff41', bg='#0a0a0a')
        stats_frame.pack(pady=10, padx=20, fill='x')
        
        self.stats_text = tk.Text(stats_frame, height=4, width=50,
                                 bg='#1a1a1a', fg='#00ff41',
                                 font=('Courier', 9))
        self.stats_text.pack(padx=10, pady=5)
        
        # سجل الأنشطة
        log_frame = tk.LabelFrame(self.root, text="📋 سجل الأنشطة", 
                                 font=('Arial', 10, 'bold'),
                                 fg='#ffffff', bg='#0a0a0a')
        log_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=70,
                                                 bg='#1a1a1a', fg='#ffffff',
                                                 font=('Courier', 8))
        self.log_text.pack(padx=10, pady=5, fill='both', expand=True)
        
        # تعليمات
        instructions = [
            "🎮 التحكم: ALT = تفعيل/إلغاء | INS = إيقاف نهائي",
            "🎯 الميزات: كشف ألوان حقيقي | تتبع قوي | التصاق محسن",
            "🎨 الألوان: أحمر، بنفسجي، وردي، بنفسجي فاتح"
        ]
        
        for instruction in instructions:
            label = tk.Label(self.root, text=instruction,
                           font=('Arial', 8), fg='#cccccc', bg='#0a0a0a')
            label.pack(pady=1)
    
    def create_slider(self, parent, text, setting_key, min_val, max_val, default_val):
        """إنشاء شريحة تحكم"""
        frame = tk.Frame(parent, bg='#0a0a0a')
        frame.pack(fill='x', padx=10, pady=3)
        
        label = tk.Label(frame, text=f"{text}: {default_val}", 
                        font=('Arial', 9), fg='#ffffff', bg='#0a0a0a')
        label.pack(anchor='w')
        
        slider = tk.Scale(frame, from_=min_val, to=max_val, 
                         orient=tk.HORIZONTAL, bg='#333333', fg='#ffffff',
                         highlightbackground='#0a0a0a', troughcolor='#555555')
        slider.set(default_val)
        slider.pack(fill='x', pady=1)
        
        def update_setting(value):
            self.settings[setting_key] = int(value)
            label.config(text=f"{text}: {value}")
        
        slider.config(command=update_setting)
    
    def log_activity(self, message):
        """تسجيل النشاط"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        print(log_message.strip())
    
    def update_stats(self):
        """تحديث الإحصائيات"""
        stats_text = f"""الأهداف المكتشفة: {self.stats['targets_detected']}
الالتصاقات المطبقة: {self.stats['locks_applied']}
أحمر: {self.stats['colors_found']['red']} | بنفسجي: {self.stats['colors_found']['purple']}
وردي: {self.stats['colors_found']['pink']} | بنفسجي فاتح: {self.stats['colors_found']['violet']}"""
        
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)
    
    def start_system(self):
        """تشغيل النظام"""
        if not self.system_active:
            self.system_active = True
            
            # تشغيل الخيوط
            threading.Thread(target=self.main_loop, daemon=True).start()
            threading.Thread(target=self.stats_updater, daemon=True).start()
            
            self.status_label.config(text="🟢 النظام نشط", fg='#00ff00')
            self.start_btn.config(state='disabled')
            self.stop_btn.config(state='normal')
            
            self.log_activity("🚀 تم تشغيل النظام المحسن")
            self.log_activity(f"💪 قوة التصويب: {self.settings['aim_strength']}")
            self.log_activity(f"🎯 قوة التتبع: {self.settings['tracking_power']}")
            self.log_activity(f"🎨 كشف الألوان: {self.settings['color_detection']}")
    
    def stop_system(self):
        """إيقاف النظام"""
        self.system_active = False
        self.status_label.config(text="🔴 النظام متوقف", fg='#ff0000')
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.log_activity("⏹️ تم إيقاف النظام")
    
    def stop_script(self):
        """إيقاف السكربت نهائياً"""
        self.log_activity("🛑 تم إيقاف السكربت بواسطة INS")
        self.root.quit()
    
    def stats_updater(self):
        """محدث الإحصائيات"""
        while self.system_active:
            try:
                self.update_stats()
                time.sleep(1)
            except:
                break
    
    def main_loop(self):
        """الحلقة الرئيسية المحسنة"""
        while self.system_active:
            try:
                # فحص مفاتيح التحكم
                if self.is_key_pressed(0x12):  # ALT
                    self.aimbot_enabled = not self.aimbot_enabled
                    status = "نشط" if self.aimbot_enabled else "متوقف"
                    self.log_activity(f"🎯 التصويب: {status}")
                    time.sleep(0.3)  # منع التكرار السريع
                
                # تطبيق التصويب إذا كان نشطاً
                if self.aimbot_enabled:
                    self.detect_and_aim()
                
                time.sleep(0.001)  # استجابة سريعة
                
            except Exception as e:
                self.log_activity(f"❌ خطأ في الحلقة الرئيسية: {e}")
                time.sleep(0.1)
    
    def detect_and_aim(self):
        """كشف الألوان والتصويب"""
        try:
            # محاكاة كشف الألوان المحسن
            if random.random() < 0.05:  # 5% احتمال للكشف
                # اختيار لون عشوائي
                colors = ['red', 'purple', 'pink', 'violet']
                detected_color = random.choice(colors)
                
                # تحديث الإحصائيات
                self.stats['targets_detected'] += 1
                self.stats['colors_found'][detected_color] += 1
                
                # تطبيق التصويب القوي
                self.apply_enhanced_aim(detected_color)
                
        except Exception as e:
            self.log_activity(f"❌ خطأ في الكشف: {e}")
    
    def apply_enhanced_aim(self, color):
        """تطبيق التصويب المحسن"""
        try:
            # حساب قوة التصويب
            base_strength = self.settings['aim_strength'] / 1000.0
            tracking_strength = self.settings['tracking_power'] / 1000.0
            lock_strength = self.settings['lock_strength'] / 1000.0
            
            # حساب الحركة المطلوبة
            max_move = self.settings['max_move_pixels']
            move_x = random.randint(-max_move, max_move) * base_strength * tracking_strength
            move_y = random.randint(-max_move, max_move) * base_strength * tracking_strength
            
            # تطبيق قوة الالتصاق
            move_x *= lock_strength
            move_y *= lock_strength
            
            # تطبيق الحركة
            if abs(move_x) > 1 or abs(move_y) > 1:
                self.move_mouse_smooth(int(move_x), int(move_y))
                self.stats['locks_applied'] += 1
                self.log_activity(f"🎯 التصاق {color}: ({int(move_x)}, {int(move_y)})")
                
        except Exception as e:
            self.log_activity(f"❌ خطأ في التصويب: {e}")
    
    def is_key_pressed(self, key_code):
        """فحص ما إذا كان المفتاح مضغوطاً"""
        try:
            return (ctypes.windll.user32.GetAsyncKeyState(key_code) & 0x8000) != 0
        except:
            return False
    
    def move_mouse_smooth(self, dx, dy):
        """تحريك الماوس بشكل سلس"""
        try:
            # الحصول على الموقع الحالي
            current_pos = ctypes.wintypes.POINT()
            ctypes.windll.user32.GetCursorPos(ctypes.byref(current_pos))
            
            # حساب الموقع الجديد
            new_x = current_pos.x + dx
            new_y = current_pos.y + dy
            
            # التأكد من الحدود
            new_x = max(0, min(self.screen_width - 1, new_x))
            new_y = max(0, min(self.screen_height - 1, new_y))
            
            # تحريك سلس بخطوات متعددة
            steps = max(1, int(math.sqrt(dx*dx + dy*dy) / 5))
            for i in range(steps):
                progress = (i + 1) / steps
                intermediate_x = current_pos.x + int(dx * progress)
                intermediate_y = current_pos.y + int(dy * progress)
                
                ctypes.windll.user32.SetCursorPos(intermediate_x, intermediate_y)
                time.sleep(0.001)  # تأخير صغير للسلاسة
                
        except Exception as e:
            self.log_activity(f"❌ خطأ في تحريك الماوس: {e}")
    
    def run(self):
        """تشغيل البرنامج"""
        print("=" * 60)
        print("🎯 MQS Enhanced Aimbot")
        print("نظام التصويب المحسن مع كشف الألوان الحقيقي")
        print("=" * 60)
        print("📋 التحكم:")
        print("• ALT = تفعيل/إلغاء التصويب")
        print("• INS = إيقاف السكربت")
        print("=" * 60)
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\n[🛑] تم إيقاف البرنامج")

def main():
    """الدالة الرئيسية"""
    try:
        app = EnhancedAimbot()
        app.run()
    except Exception as e:
        print(f"[❌] خطأ في تشغيل البرنامج: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
