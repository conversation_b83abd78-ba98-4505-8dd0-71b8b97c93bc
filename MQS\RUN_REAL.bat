@echo off
cd /d "%~dp0"
title MQS Real Aimbot - Actually Works

cls
echo ================================================================
echo MQS Real Aimbot - يعمل فعلاً مع Overwatch 2
echo ================================================================
echo.

echo Checking administrator privileges...
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [WARNING] Administrator privileges recommended for best performance
    echo You can continue without admin rights, but some features may be limited
    echo.
    timeout /t 3 >nul
)

echo Checking Python...
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Python not found
    echo Install Python from python.org
    pause
    exit
)
echo [OK] Python found

echo.
echo Checking required libraries...
python -c "import cv2, mss, keyboard, numpy" >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Required libraries missing
    echo Installing libraries...
    pip install opencv-python mss keyboard numpy pywin32
    echo.
    echo Please restart this script after installation
    pause
    exit
)
echo [OK] All libraries found

echo.
echo Checking files...
if not exist "advanced_real_aimbot.py" (
    if not exist "real_aimbot.py" (
        echo [ERROR] No real aimbot files found
        pause
        exit
    )
)

echo.
echo ================================================================
echo REAL AIMBOT - ACTUALLY WORKS WITH OVERWATCH 2
echo ================================================================
echo.
echo Features:
echo - Real color detection in Overwatch 2
echo - Actual mouse movement and aiming
echo - Advanced target prediction
echo - Multiple enemy color support
echo - Smooth and natural movement
echo - Performance optimized
echo.
echo Controls:
echo - ALT       = Toggle Aimbot ON/OFF
echo - CTRL+ALT  = Strong Mode (3 seconds)
echo - F1        = Increase Strength
echo - F2        = Decrease Strength  
echo - F3        = Change Tracking Mode
echo - F4        = Reset Settings
echo - INS       = Stop Script
echo.
echo IMPORTANT SETUP:
echo 1. Launch Overwatch 2 FIRST
echo 2. Set game to Windowed or Borderless mode
echo 3. Make sure game resolution is 1920x1080
echo 4. Close other overlay programs
echo 5. Aim near enemies and press ALT
echo.

set /p choice="Choose version: [1] Advanced (Recommended) [2] Basic: "

if "%choice%"=="1" (
    if exist "advanced_real_aimbot.py" (
        echo Starting Advanced Real Aimbot...
        echo.
        python advanced_real_aimbot.py
    ) else (
        echo Advanced version not found, starting basic...
        python real_aimbot.py
    )
) else (
    if exist "real_aimbot.py" (
        echo Starting Basic Real Aimbot...
        echo.
        python real_aimbot.py
    ) else (
        echo Basic version not found, starting advanced...
        python advanced_real_aimbot.py
    )
)

echo.
echo Program finished
pause
