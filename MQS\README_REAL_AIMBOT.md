# MQS Real Aimbot for Overwatch 2
## نظام التصويب الحقيقي المحسن

### 🎯 المميزات الجديدة
- **كشف ألوان حقيقي**: يكشف الأعداء بالألوان السبعة في اللعبة فعلياً
- **تصويب حقيقي**: يحرك الماوس فعلياً للالتصاق بالأعداء
- **تعويض ارتداد حقيقي**: يتحكم في ارتداد السلاح أثناء الإطلاق
- **كشف إطلاق النار**: يكتشف متى تطلق النار لتطبيق التعويض
- **واجهة عربية محسنة**: تصميم أفضل وأوضح

### 🚀 طريقة التشغيل

#### 1. التشغيل السريع
```bash
python run_real_aimbot.py
```

#### 2. التشغيل اليدوي
```bash
# تثبيت المتطلبات
pip install opencv-python numpy mss pillow keyboard mouse

# تشغيل البرنامج
python mqs_arabic_aimbot.py
```

### ⚙️ المتطلبات
- Python 3.7+
- OpenCV (للكشف الحقيقي)
- NumPy (للمعالجة)
- MSS (لالتقاط الشاشة)
- Pillow (للصور)
- Keyboard & Mouse (للتحكم)

### 🎮 التحكم
- **ALT**: تفعيل/إلغاء تفعيل التصويب (Toggle)
- **INS**: إيقاف السكربت نهائياً
- **الواجهة**: ضبط القوة والإعدادات

### 🎨 الألوان المدعومة
1. **أحمر** (Red) - أعداء حمر
2. **بنفسجي** (Purple) - أعداء بنفسجية  
3. **بنفسجي فاتح** (Violet) - أعداء بنفسجية فاتحة
4. **وردي** (Pink) - أعداء وردية
5. **برتقالي** (Orange) - أعداء برتقالية
6. **أصفر** (Yellow) - أعداء صفراء
7. **سماوي** (Cyan) - أعداء سماوية

### ⚡ الإعدادات المحسنة
- **قوة التصويب**: 0-100% (افتراضي 85%)
- **سرعة التصويب**: 0-100% (افتراضي 75%)
- **قوة كشف الألوان**: 0-100% (افتراضي 90%)
- **قوة تعويض الارتداد**: 0-100% (افتراضي 80%)

### 🔧 كيف يعمل النظام

#### 1. كشف الألوان
- يلتقط الشاشة في الوقت الفعلي
- يحول الصورة إلى HSV للكشف الأفضل
- يبحث عن الألوان السبعة المحددة
- يحدد أقرب هدف لمركز الشاشة

#### 2. التصويب الحقيقي
- يحسب المسافة بين الهدف ومركز الشاشة
- يطبق قوة وسرعة التصويب المحددة
- يحرك الماوس بشكل طبيعي وسلس
- يتجنب الحركات المشبوهة الكبيرة

#### 3. تعويض الارتداد
- يكتشف متى تطلق النار (زر الماوس الأيسر)
- يطبق حركة عكسية لتعويض ارتداد السلاح
- يعمل مع جميع أنواع الأسلحة

### 🛡️ الأمان
- حركات طبيعية للماوس
- تأخيرات واقعية
- تجنب الحركات المشبوهة
- عدم التدخل في ملفات اللعبة

### ⚠️ تحذيرات مهمة
1. **صلاحيات المدير**: مطلوبة للوصول للعبة
2. **إغلاق مضاد الفيروسات**: قد يحجب البرنامج
3. **تشغيل اللعبة أولاً**: تأكد من تشغيل Overwatch 2
4. **الدقة المناسبة**: يعمل أفضل مع 1920x1080

### 🐛 حل المشاكل

#### المشكلة: لا يكتشف الأعداء
- تأكد من تثبيت opencv-python
- تأكد من صلاحيات المدير
- اضبط قوة كشف الألوان

#### المشكلة: لا يحرك الماوس
- تأكد من صلاحيات المدير
- تأكد من تشغيل اللعبة
- اضبط قوة التصويب

#### المشكلة: لا يعمل تعويض الارتداد
- تأكد من تفعيل الخاصية
- اضبط قوة تعويض الارتداد
- تأكد من الإطلاق بزر الماوس الأيسر

### 📊 الإحصائيات
- عدد الأهداف المكتشفة لكل لون
- عدد مرات الالتصاق
- عدد مرات تعويض الارتداد
- معدل الكشف في الثانية

### 🔄 التحديثات
- v3.0: نظام حقيقي بدلاً من المحاكاة
- كشف ألوان محسن
- تصويب أكثر دقة
- تعويض ارتداد فعال
- واجهة عربية أفضل

### 📞 الدعم
إذا واجهت مشاكل:
1. تأكد من تثبيت جميع المتطلبات
2. شغل البرنامج بصلاحيات المدير
3. تأكد من تشغيل اللعبة
4. اضبط الإعدادات حسب حاجتك

---
**ملاحظة**: هذا البرنامج للأغراض التعليمية. استخدمه بمسؤوليتك الخاصة.
