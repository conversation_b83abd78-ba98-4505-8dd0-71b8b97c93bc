#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Overwatch 2 Real Aimbot - Professional Grade
يعمل فعلياً داخل لعبة Overwatch 2
"""

import cv2
import numpy as np
import pyautogui
import win32api
import win32con
import win32gui
import time
import threading
import keyboard
import mss
from ctypes import windll, Structure, c_long, byref
import math
import random

class Point(Structure):
    _fields_ = [("x", c_long), ("y", c_long)]

class RealOverwatchAimbot:
    def __init__(self):
        self.running = False
        self.aim_assist_active = False
        self.recoil_control_active = False
        
        # إعدادات قوية للتتبع
        self.aim_strength = 0.95  # قوة عالية جداً
        self.tracking_speed = 0.8  # سرعة تتبع عالية
        self.smoothing = 0.1  # تنعيم قليل للحركة الطبيعية
        
        # ألوان الأعداء في Overwatch 2
        self.enemy_colors = {
            'red': ([0, 0, 150], [80, 80, 255]),      # أحمر قوي
            'purple': ([100, 0, 100], [255, 100, 255]), # بنفسجي
            'violet': ([130, 0, 130], [255, 130, 255]), # بنفسجي فاتح
            'pink': ([150, 100, 150], [255, 200, 255]), # وردي
            'orange': ([0, 100, 200], [100, 200, 255])  # برتقالي
        }
        
        # منطقة البحث (وسط الشاشة)
        self.search_radius = 200
        self.screen_center = None
        self.last_target = None
        
        # إعدادات التحكم في الارتداد
        self.recoil_patterns = {
            'default': [(0, -2), (1, -3), (-1, -2), (0, -4), (2, -3)]
        }
        
        self.setup_screen()
        
    def setup_screen(self):
        """إعداد معلومات الشاشة"""
        self.screen_width = win32api.GetSystemMetrics(0)
        self.screen_height = win32api.GetSystemMetrics(1)
        self.screen_center = (self.screen_width // 2, self.screen_height // 2)
        
        # تعطيل fail-safe في pyautogui
        pyautogui.FAILSAFE = False
        pyautogui.PAUSE = 0
        
    def get_cursor_pos(self):
        """الحصول على موقع المؤشر الحالي"""
        pt = Point()
        windll.user32.GetCursorPos(byref(pt))
        return pt.x, pt.y
        
    def move_mouse_smooth(self, target_x, target_y):
        """تحريك الماوس بسلاسة نحو الهدف"""
        current_x, current_y = self.get_cursor_pos()
        
        # حساب المسافة والاتجاه
        dx = target_x - current_x
        dy = target_y - current_y
        distance = math.sqrt(dx*dx + dy*dy)
        
        if distance < 5:  # إذا كان قريب جداً
            return
            
        # تطبيق قوة التتبع
        move_x = dx * self.aim_strength * self.tracking_speed
        move_y = dy * self.aim_strength * self.tracking_speed
        
        # إضافة تنعيم طبيعي
        move_x += random.uniform(-1, 1) * self.smoothing
        move_y += random.uniform(-1, 1) * self.smoothing
        
        # تحريك الماوس
        new_x = int(current_x + move_x)
        new_y = int(current_y + move_y)
        
        # استخدام win32api للحركة المباشرة
        win32api.SetCursorPos((new_x, new_y))
        
    def capture_screen_region(self):
        """التقاط منطقة من الشاشة حول المؤشر"""
        current_x, current_y = self.get_cursor_pos()
        
        # تحديد منطقة البحث
        left = max(0, current_x - self.search_radius)
        top = max(0, current_y - self.search_radius)
        width = min(self.search_radius * 2, self.screen_width - left)
        height = min(self.search_radius * 2, self.screen_height - top)
        
        with mss.mss() as sct:
            monitor = {
                "top": top,
                "left": left,
                "width": width,
                "height": height
            }
            screenshot = sct.grab(monitor)
            img = np.array(screenshot)
            return cv2.cvtColor(img, cv2.COLOR_BGRA2BGR), left, top
            
    def detect_enemies(self, img, offset_x, offset_y):
        """كشف الأعداء بناءً على الألوان"""
        targets = []
        
        for color_name, (lower, upper) in self.enemy_colors.items():
            # تحويل إلى HSV للكشف الأفضل
            hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
            
            # إنشاء قناع للون
            lower_np = np.array(lower)
            upper_np = np.array(upper)
            mask = cv2.inRange(hsv, lower_np, upper_np)
            
            # تطبيق مرشحات لتحسين الكشف
            kernel = np.ones((3,3), np.uint8)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            
            # العثور على الكونتورات
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 50:  # تصفية الأهداف الصغيرة
                    # حساب مركز الهدف
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"]) + offset_x
                        cy = int(M["m01"] / M["m00"]) + offset_y
                        targets.append((cx, cy, area, color_name))
                        
        return targets
        
    def get_best_target(self, targets):
        """اختيار أفضل هدف للتتبع"""
        if not targets:
            return None
            
        current_x, current_y = self.get_cursor_pos()
        
        # ترتيب الأهداف حسب القرب والحجم
        scored_targets = []
        for x, y, area, color in targets:
            distance = math.sqrt((x - current_x)**2 + (y - current_y)**2)
            # تفضيل الأهداف القريبة والكبيرة
            score = area / (distance + 1)
            scored_targets.append((score, x, y, color))
            
        # إرجاع أفضل هدف
        scored_targets.sort(reverse=True)
        return scored_targets[0][1:3]  # إرجاع x, y فقط
        
    def apply_recoil_control(self):
        """تطبيق التحكم في الارتداد"""
        if not self.recoil_control_active:
            return
            
        # تحريك الماوس لأسفل لمقاومة الارتداد
        current_x, current_y = self.get_cursor_pos()
        recoil_y = random.randint(2, 5)  # حركة عشوائية لأسفل
        win32api.SetCursorPos((current_x, current_y + recoil_y))
        
    def aim_assist_loop(self):
        """حلقة التتبع الرئيسية"""
        while self.running:
            try:
                if self.aim_assist_active:
                    # التقاط الشاشة
                    img, offset_x, offset_y = self.capture_screen_region()
                    
                    # كشف الأعداء
                    targets = self.detect_enemies(img, offset_x, offset_y)
                    
                    # اختيار أفضل هدف
                    best_target = self.get_best_target(targets)
                    
                    if best_target:
                        target_x, target_y = best_target
                        # تحريك المؤشر نحو الهدف
                        self.move_mouse_smooth(target_x, target_y)
                        self.last_target = best_target
                        
                    # تطبيق التحكم في الارتداد إذا كان مفعل
                    if keyboard.is_pressed('mouse left'):
                        self.apply_recoil_control()
                        
                time.sleep(0.01)  # تحديث سريع جداً
                
            except Exception as e:
                print(f"خطأ في حلقة التتبع: {e}")
                time.sleep(0.1)
                
    def start(self):
        """بدء تشغيل البرنامج"""
        print("=== Overwatch 2 Real Aimbot ===")
        print("ALT: تفعيل/إيقاف Aim Assist")
        print("CTRL: تفعيل/إيقاف Recoil Control") 
        print("INS: إيقاف البرنامج")
        print("البرنامج يعمل الآن...")
        
        self.running = True
        
        # بدء حلقة التتبع في خيط منفصل
        aim_thread = threading.Thread(target=self.aim_assist_loop, daemon=True)
        aim_thread.start()
        
        # حلقة التحكم الرئيسية
        while self.running:
            try:
                # تبديل Aim Assist بـ ALT
                if keyboard.is_pressed('alt'):
                    if not hasattr(self, 'alt_pressed'):
                        self.alt_pressed = True
                        self.aim_assist_active = not self.aim_assist_active
                        status = "مفعل" if self.aim_assist_active else "معطل"
                        print(f"Aim Assist: {status}")
                        time.sleep(0.3)
                else:
                    self.alt_pressed = False
                    
                # تبديل Recoil Control بـ CTRL
                if keyboard.is_pressed('ctrl'):
                    if not hasattr(self, 'ctrl_pressed'):
                        self.ctrl_pressed = True
                        self.recoil_control_active = not self.recoil_control_active
                        status = "مفعل" if self.recoil_control_active else "معطل"
                        print(f"Recoil Control: {status}")
                        time.sleep(0.3)
                else:
                    self.ctrl_pressed = False
                    
                # إيقاف البرنامج بـ INS
                if keyboard.is_pressed('insert'):
                    print("إيقاف البرنامج...")
                    self.running = False
                    break
                    
                time.sleep(0.05)
                
            except KeyboardInterrupt:
                self.running = False
                break
                
        print("تم إيقاف البرنامج.")

if __name__ == "__main__":
    try:
        aimbot = RealOverwatchAimbot()
        aimbot.start()
    except Exception as e:
        print(f"خطأ في تشغيل البرنامج: {e}")
        input("اضغط Enter للخروج...")
