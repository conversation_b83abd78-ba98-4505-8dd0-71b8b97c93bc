@echo off
cd /d "%~dp0"
title MQS Simple Aimbot - Working Version

echo ================================================================
echo MQS Simple Aimbot - Working Version
echo ================================================================
echo.

echo Checking administrator privileges...
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Need Administrator privileges
    echo Right-click and "Run as administrator"
    pause
    exit
)
echo [OK] Administrator privileges granted

echo.
echo Checking Python...
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Python not found
    echo Install Python from python.org
    pause
    exit
)
echo [OK] Python found

echo.
echo Checking files...
if not exist "simple_aimbot.py" (
    echo [ERROR] simple_aimbot.py not found
    echo Make sure you are in the correct directory
    pause
    exit
)
echo [OK] simple_aimbot.py found

echo.
echo Starting Simple Aimbot...
echo.
echo Controls:
echo - ALT = Toggle Aimbot
echo - INS = Stop Script
echo.
echo Settings:
echo - Aim Strength: 1000 (Ultra Strong)
echo - Tracking Power: 600 (Very Strong)
echo - Lock Strength: 400 (Strong)
echo.

python simple_aimbot.py

echo.
echo Program finished
pause
