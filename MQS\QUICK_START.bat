@echo off
cd /d "%~dp0"
title MQS Quick Start

cls
echo ================================================================
echo MQS Aimbot - Quick Start
echo ================================================================
echo.

REM Check admin
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Need Administrator privileges
    echo Right-click this file and select "Run as administrator"
    pause
    exit
)

REM Check Python
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Python not installed
    pause
    exit
)

REM Check files and run the best available version
if exist "enhanced_aimbot.py" (
    echo [INFO] Starting Enhanced Aimbot...
    echo [INFO] Controls: ALT = Toggle, INS = Stop
    echo.
    python enhanced_aimbot.py
) else if exist "simple_aimbot.py" (
    echo [INFO] Starting Simple Aimbot...
    echo [INFO] Controls: ALT = Toggle, INS = Stop
    echo.
    python simple_aimbot.py
) else if exist "mqs_arabic_aimbot.py" (
    echo [INFO] Starting Original Aimbot...
    echo [INFO] Controls: ALT = Toggle, INS = Stop
    echo.
    python mqs_arabic_aimbot.py
) else (
    echo [ERROR] No aimbot files found
    echo Make sure you are in the correct directory
)

echo.
pause
