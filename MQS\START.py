#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQS Ultra Tracking - Simple Launcher
"""

import sys
import os
import ctypes

def check_admin():
    """Check if running as administrator"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def main():
    print("=" * 50)
    print("🎯 MQS Ultra Tracking Aimbot")
    print("=" * 50)
    
    # Check admin privileges
    if not check_admin():
        print("❌ Administrator privileges required!")
        print("Right-click and 'Run as administrator'")
        input("Press Enter to exit...")
        return
    
    print("✅ Administrator privileges OK")
    
    # Import and run
    try:
        print("🚀 Starting Ultra Tracking System...")
        from mqs_arabic_aimbot import MQSArabicAimBot
        
        print("✅ System loaded successfully")
        print("\n📋 Controls:")
        print("• ALT = Toggle Ultra Tracking")
        print("• INS = Stop Script")
        print("• Adjust settings in GUI")
        print("\n🎯 Ultra Settings Active:")
        print("• Tracking Power: 600%")
        print("• Lock Strength: 400%") 
        print("• Color Detection: 400%")
        print("• Movement Prediction: ON")
        print("• Aggressive Tracking: ON")
        print("\n" + "=" * 50)
        
        # Create and run
        app = MQSArabicAimBot()
        app.run()
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("Make sure mqs_arabic_aimbot.py exists")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n👋 Program finished")
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
