#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQS Real Aimbot - Actually Works with Overwatch 2
نظام التصويب الحقيقي الذي يعمل فعلاً مع أوفرواتش 2
"""

import cv2
import numpy as np
import mss
import time
import threading
import ctypes
import ctypes.wintypes
from ctypes import wintypes
import keyboard
import math
import random

class RealAimbot:
    def __init__(self):
        print("🎯 MQS Real Aimbot - يعمل فعلاً مع Overwatch 2")
        print("=" * 60)
        
        # إعدادات النظام
        self.running = False
        self.aimbot_active = False
        self.target_found = False
        
        # إعدادات القوة
        self.aim_strength = 1.5      # قوة التصويب
        self.tracking_speed = 0.8    # سرعة التتبع
        self.lock_strength = 2.0     # قوة الالتصاق
        self.smoothing = 0.3         # نعومة الحركة
        
        # إعدادات الشاشة
        self.screen_width = ctypes.windll.user32.GetSystemMetrics(0)
        self.screen_height = ctypes.windll.user32.GetSystemMetrics(1)
        self.center_x = self.screen_width // 2
        self.center_y = self.screen_height // 2
        
        # منطقة البحث (حول وسط الشاشة)
        self.search_radius = 300
        
        # إعداد التقاط الشاشة
        self.sct = mss.mss()
        
        # ألوان Overwatch 2 الحقيقية (HSV)
        self.enemy_colors = [
            # أحمر
            ([0, 120, 120], [10, 255, 255]),
            ([170, 120, 120], [180, 255, 255]),
            # بنفسجي/وردي
            ([140, 100, 100], [170, 255, 255]),
            ([280, 100, 100], [320, 255, 255]),
            # برتقالي
            ([10, 120, 120], [25, 255, 255]),
        ]
        
        # إعداد المفاتيح
        self.setup_hotkeys()
        
        print("✅ تم تهيئة النظام")
        print("📋 التحكم:")
        print("  ALT = تفعيل/إلغاء التصويب")
        print("  INS = إيقاف السكربت")
        print("  F1  = زيادة القوة")
        print("  F2  = تقليل القوة")
        print("=" * 60)
    
    def setup_hotkeys(self):
        """إعداد المفاتيح الساخنة"""
        try:
            keyboard.add_hotkey('alt', self.toggle_aimbot)
            keyboard.add_hotkey('insert', self.stop_script)
            keyboard.add_hotkey('f1', self.increase_strength)
            keyboard.add_hotkey('f2', self.decrease_strength)
            print("✅ تم تفعيل المفاتيح الساخنة")
        except Exception as e:
            print(f"⚠️ خطأ في المفاتيح: {e}")
    
    def toggle_aimbot(self):
        """تفعيل/إلغاء التصويب"""
        self.aimbot_active = not self.aimbot_active
        status = "نشط 🟢" if self.aimbot_active else "متوقف 🔴"
        print(f"🎯 التصويب: {status}")
    
    def stop_script(self):
        """إيقاف السكربت"""
        print("🛑 إيقاف السكربت...")
        self.running = False
    
    def increase_strength(self):
        """زيادة قوة التصويب"""
        self.aim_strength = min(3.0, self.aim_strength + 0.2)
        print(f"⬆️ قوة التصويب: {self.aim_strength:.1f}")
    
    def decrease_strength(self):
        """تقليل قوة التصويب"""
        self.aim_strength = max(0.5, self.aim_strength - 0.2)
        print(f"⬇️ قوة التصويب: {self.aim_strength:.1f}")
    
    def capture_screen(self):
        """التقاط منطقة حول وسط الشاشة"""
        try:
            # تحديد منطقة البحث
            left = self.center_x - self.search_radius
            top = self.center_y - self.search_radius
            width = self.search_radius * 2
            height = self.search_radius * 2
            
            monitor = {
                "top": top,
                "left": left,
                "width": width,
                "height": height
            }
            
            # التقاط الشاشة
            screenshot = self.sct.grab(monitor)
            img = np.array(screenshot)
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
            return img
        except Exception as e:
            print(f"❌ خطأ في التقاط الشاشة: {e}")
            return None
    
    def detect_enemies(self, img):
        """كشف الأعداء بالألوان"""
        if img is None:
            return None
        
        try:
            # تحويل إلى HSV
            hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
            
            # إنشاء قناع للألوان
            mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
            
            for lower, upper in self.enemy_colors:
                lower = np.array(lower)
                upper = np.array(upper)
                color_mask = cv2.inRange(hsv, lower, upper)
                mask = cv2.bitwise_or(mask, color_mask)
            
            # تنظيف القناع
            kernel = np.ones((3, 3), np.uint8)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            
            # العثور على الكونتورات
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if contours:
                # العثور على أكبر كونتور
                largest_contour = max(contours, key=cv2.contourArea)
                area = cv2.contourArea(largest_contour)
                
                # التحقق من الحد الأدنى للمساحة
                if area > 50:  # حد أدنى للمساحة
                    # حساب المركز
                    M = cv2.moments(largest_contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])
                        
                        # تحويل إلى إحداثيات الشاشة
                        screen_x = (self.center_x - self.search_radius) + cx
                        screen_y = (self.center_y - self.search_radius) + cy
                        
                        return (screen_x, screen_y, area)
            
            return None
            
        except Exception as e:
            print(f"❌ خطأ في كشف الأعداء: {e}")
            return None
    
    def move_mouse_smooth(self, target_x, target_y):
        """تحريك الماوس بشكل سلس نحو الهدف"""
        try:
            # الحصول على الموقع الحالي
            current_pos = wintypes.POINT()
            ctypes.windll.user32.GetCursorPos(ctypes.byref(current_pos))
            
            # حساب المسافة
            dx = target_x - current_pos.x
            dy = target_y - current_pos.y
            distance = math.sqrt(dx*dx + dy*dy)
            
            if distance < 5:  # إذا كان قريب جداً
                return
            
            # تطبيق قوة التصويب
            move_x = dx * self.aim_strength * self.tracking_speed
            move_y = dy * self.aim_strength * self.tracking_speed
            
            # تطبيق النعومة
            move_x *= self.smoothing
            move_y *= self.smoothing
            
            # إضافة عشوائية طفيفة للطبيعية
            move_x += random.uniform(-0.5, 0.5)
            move_y += random.uniform(-0.5, 0.5)
            
            # حساب الموقع الجديد
            new_x = current_pos.x + int(move_x)
            new_y = current_pos.y + int(move_y)
            
            # التأكد من الحدود
            new_x = max(0, min(self.screen_width - 1, new_x))
            new_y = max(0, min(self.screen_height - 1, new_y))
            
            # تحريك الماوس
            ctypes.windll.user32.SetCursorPos(new_x, new_y)
            
        except Exception as e:
            print(f"❌ خطأ في تحريك الماوس: {e}")
    
    def aim_at_target(self, target):
        """التصويب على الهدف"""
        if target is None:
            self.target_found = False
            return
        
        target_x, target_y, area = target
        
        # تطبيق قوة الالتصاق حسب حجم الهدف
        lock_multiplier = min(2.0, area / 100.0)
        effective_lock = self.lock_strength * lock_multiplier
        
        # تحريك الماوس نحو الهدف
        self.move_mouse_smooth(target_x, target_y)
        
        self.target_found = True
    
    def main_loop(self):
        """الحلقة الرئيسية"""
        print("🚀 بدء التشغيل...")
        self.running = True
        
        frame_count = 0
        last_status_time = time.time()
        
        while self.running:
            try:
                if self.aimbot_active:
                    # التقاط الشاشة
                    img = self.capture_screen()
                    
                    if img is not None:
                        # كشف الأعداء
                        target = self.detect_enemies(img)
                        
                        # التصويب على الهدف
                        self.aim_at_target(target)
                        
                        frame_count += 1
                        
                        # طباعة الحالة كل 5 ثوان
                        current_time = time.time()
                        if current_time - last_status_time > 5:
                            status = "🎯 هدف مكتشف" if self.target_found else "🔍 بحث عن أهداف"
                            print(f"{status} | الإطارات: {frame_count} | القوة: {self.aim_strength:.1f}")
                            last_status_time = current_time
                            frame_count = 0
                
                # تأخير صغير لتجنب استهلاك CPU عالي
                time.sleep(0.001)  # 1ms
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ خطأ في الحلقة الرئيسية: {e}")
                time.sleep(0.1)
        
        print("⏹️ تم إيقاف النظام")
    
    def run(self):
        """تشغيل النظام"""
        try:
            self.main_loop()
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف البرنامج بواسطة المستخدم")
        except Exception as e:
            print(f"❌ خطأ عام: {e}")

def main():
    """الدالة الرئيسية"""
    print("تحقق من المتطلبات...")
    
    # التحقق من المكتبات
    try:
        import cv2
        import mss
        import keyboard
        print("✅ جميع المكتبات متوفرة")
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("قم بتثبيت المكتبات: pip install opencv-python mss keyboard")
        input("اضغط Enter للخروج...")
        return
    
    # تشغيل النظام
    try:
        aimbot = RealAimbot()
        aimbot.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
