@echo off
title MQS Arabic AimBot - Final Version
color 0A

echo.
echo ================================================================
echo    MQS Arabic AimBot - Final Version
echo ================================================================
echo.
echo Enhanced Arabic Interface - 300%% Color Detection - 200%% Power
echo 7 Colors: Red, Purple, Violet, Pink, Orange, Yellow, Cyan
echo Extra Features: Auto-Fire, Smart Prediction, Enemy Tracking
echo INS Key: Instant Stop
echo.
echo ================================================================
echo.

echo [INFO] Checking requirements...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not installed! Please install Python first
    echo [INFO] Download from: https://python.org
    pause
    exit /b 1
)

echo [OK] Python available
echo.

echo [INFO] Installing required libraries...
echo.

pip install opencv-python numpy mss pywin32 psutil keyboard --quiet --disable-pip-version-check
if errorlevel 1 (
    echo [WARNING] Failed to install some libraries
    echo [INFO] Trying alternative installation...
    python -m pip install opencv-python numpy mss pywin32 psutil keyboard --quiet
)

echo [OK] Libraries installed
echo.

echo [INFO] Starting MQS Arabic AimBot - Final Version...
echo [INFO] Features: 200%% Power + 300%% Color Detection + Extra Features
echo [INFO] Press INS key anytime to stop the script instantly
echo.
echo [WARNING] This system is for educational purposes only
echo [WARNING] Use at your own risk
echo.

timeout /t 3 /nobreak >nul

python mqs_arabic_aimbot.py

if errorlevel 1 (
    echo.
    echo [ERROR] Failed to start the system
    echo [INFO] Check:
    echo    - Python installation
    echo    - All libraries available
    echo    - Administrator privileges
    echo.
    pause
) else (
    echo.
    echo [OK] System closed successfully
    echo.
)

pause
