#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات متقدمة لنظام MQS Aimbot
Advanced Configuration for MQS Aimbot System
"""

# ===== إعدادات التصويب الأساسية =====
# Basic Aim Settings

# قوة التصويب (0-500) - كلما زادت كلما كان التصويب أقوى
AIM_STRENGTH = 1000

# سرعة التصويب (0-500) - كلما زادت كلما كان التصويب أسرع  
AIM_SPEED = 250

# قوة الالتصاق (0-500) - قوة التصاق الماوس بالهدف
LOCK_STRENGTH = 400

# قوة التتبع (0-500) - قوة تتبع الأعداء المتحركين
TRACKING_STRENGTH = 450

# ===== إعدادات متقدمة للتتبع =====
# Advanced Tracking Settings

# مفتاح التفعيل (1 = زر الماوس الأيسر، 2 = زر الماوس الأيمن)
AIM_KEY = 1

# وضع التصويب (0 = تتبع مستمر، 1 = نقر سريع)
AIM_MODE = 0

# حساسية اللعبة (يجب أن تطابق إعدادات اللعبة)
SENSITIVITY = 1.0

# معدل الإطارات للمسح (30-120)
FPS = 50

# مدة التصويب لكل نقرة (بالثواني)
AIM_DURATION_MILLIS = 0.78

# ===== إعدادات الحركة =====
# Movement Settings

# أقصى بكسل للحركة في الإطار الواحد (1-50)
AIM_MAX_MOVE_PIXELS = 20

# نسبة العشوائية الطبيعية (0-100)
AIM_JITTER_PERCENT = 64

# إزاحة التصويب الأفقية (0.5-1.5)
AIM_OFFSET_X = 1.00

# إزاحة التصويب العمودية (0.5-1.5) 
AIM_OFFSET_Y = 0.98

# ===== إعدادات كشف الألوان =====
# Color Detection Settings

# قوة كشف الألوان (0-500)
COLOR_DETECTION_STRENGTH = 400

# تحمل الألوان (5-50) - كلما زاد كلما كشف ألوان أكثر
TARGET_COLOR_TOLERANCE = 30

# الحد الأدنى لعرض الهدف (بكسل)
AIM_MIN_TARGET_WIDTH = 1

# الحد الأدنى لارتفاع الهدف (بكسل)
AIM_MIN_TARGET_HEIGHT = 1

# عرض صندوق البحث (بكسل)
BOX_WIDTH = 80

# ارتفاع صندوق البحث (بكسل)
BOX_HEIGHT = 80

# ===== إعدادات التحكم في الارتداد =====
# Recoil Control Settings

# قوة تعويض الارتداد (0-500)
RECOIL_CONTROL_STRENGTH = 350

# تفعيل تعويض الارتداد التلقائي
AUTO_RECOIL_COMPENSATION = True

# ===== إعدادات التنبؤ =====
# Prediction Settings

# تفعيل التنبؤ بحركة العدو
PREDICTION_ENABLED = True

# عدد الإطارات للتنبؤ (1-10)
TARGET_PREDICTION_FRAMES = 4

# قوة التنبؤ (0-300)
PREDICTION_POWER = 200

# ===== إعدادات التتبع المتقدمة =====
# Advanced Tracking Settings

# تفعيل التتبع السلس
SMOOTH_TRACKING = True

# تفعيل التتبع العدواني
AGGRESSIVE_TRACKING = True

# تفعيل التصويب اللاصق
STICKY_AIM = True

# قوة التتبع الفائقة (0-1000)
ULTRA_TRACKING_STRENGTH = 600

# ===== إعدادات الإطلاق السريع =====
# Quick Fire Settings

# بكسل الإطلاق السريع
FLICK_SHOOT_PIXELS = 8

# مدة التوقف بعد الإطلاق السريع (ms)
FLICK_PAUSE_DURATION = 30

# ===== إعدادات النظام =====
# System Settings

# تفعيل العرض المرئي
ENABLE_OVERLAY = False

# عنوان النافذة للكشف
WINDOW_TITLE_SEARCH = "Overwatch"

# معرف الجهاز
DEVICE_ID = 11

# ===== ألوان Overwatch 2 المحددة =====
# Specific Overwatch 2 Target Colors

OVERWATCH_TARGET_COLORS = [
    'd521cd', 'd722cf', 'd623ce', 'd722ce', 'd621cd', 'ce19ca', 'd11ccb', 'd21dca',
    'c818cf', 'd722cd', 'd722ce', 'cd19c9', 'c617d3', 'cb17c5', 'da25d3', 'ce24cc',
    'd328cc', 'db32ef', 'bd15c4', 'dc5bea', 'da59eb', 'd959e9', 'f444fb', 'cf1ac9',
    'd422d4', 'd923cd', 'e53af2', 'd321d3', 'e539f3', 'e035ed', 'd822cc', 'e83df5',
    'd11fd1', 'd622d0', 'd21dcc', 'd429e2', 'e537ef', 'd923cd', 'e136ee', 'd321d3',
    'e63bf3', 'd722cf', 'e036ee', 'd72ce6', 'd428e1', 'd321d3', 'd21dcc', 'df34ed',
    'd822cc', 'e434e6', 'd43ddf', 'de30e4', 'be0dbe', 'd823d3', 'c814c4', 'c20ab7',
    'de1ec1', 'ca16c6', 'c30ebe', 'bb0fbf', 'c510bf', 'c10cbc', 'd21cb6', 'ca14c5',
    'b80cd1', 'ae0ea8', 'bf0ec3', 'd415c1', 'bc22b7', 'd317c4', 'b1179d', 'bc0fb4',
    'cc47c7', 'b834b5', 'dc2cd9', 'd727d5', 'de30da', 'c834c6'
]

# ===== إعدادات الأمان =====
# Safety Settings

# الحد الأقصى للحركة المشبوهة (بكسل)
MAX_SUSPICIOUS_MOVEMENT = 100

# تأخير الأمان بين الحركات (ms)
SAFETY_DELAY = 0.5

# تفعيل الحركة الطبيعية
NATURAL_MOVEMENT = True

# ===== ملاحظات مهمة =====
"""
Important Notes:

1. قم بضبط AIM_STRENGTH و TRACKING_STRENGTH حسب حاجتك
2. إذا كان التتبع ضعيف، زد ULTRA_TRACKING_STRENGTH
3. إذا كان التصويب سريع جداً، قلل AIM_SPEED
4. إذا لم يكشف الأعداء، زد COLOR_DETECTION_STRENGTH
5. تأكد من مطابقة SENSITIVITY مع إعدادات اللعبة

Adjust these settings based on your needs:
- Increase TRACKING_STRENGTH for stronger enemy tracking
- Increase AIM_STRENGTH for more powerful aim assistance  
- Increase COLOR_DETECTION_STRENGTH if enemies aren't detected
- Match SENSITIVITY with your in-game settings
"""
