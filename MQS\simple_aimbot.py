#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQS Simple Working Aimbot
نظام التصويب البسيط الذي يعمل
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import random
import math
import ctypes
import ctypes.wintypes
import sys
import os

# محاولة تحميل المكتبات المتقدمة
try:
    import keyboard
    import cv2
    import numpy as np
    import mss
    LIBS_AVAILABLE = True
    print("[✅] جميع المكتبات متوفرة - الوضع الحقيقي نشط")
except ImportError as e:
    LIBS_AVAILABLE = False
    print(f"[⚠️] مكتبات مفقودة: {e}")
    print("[ℹ️] سيعمل البرنامج في وضع المحاكاة")

class SimpleAimbot:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 MQS Simple Aimbot - Working Version")
        self.root.geometry("600x500")
        self.root.configure(bg='#1a1a1a')
        
        # متغيرات النظام
        self.system_active = False
        self.aimbot_enabled = False
        self.aim_assist_enabled = False
        self.recoil_enabled = False
        
        # إعدادات القوة
        self.settings = {
            'aim_strength': 1000,      # قوة فائقة
            'tracking_power': 600,     # تتبع قوي
            'lock_strength': 400,      # التصاق قوي
            'recoil_strength': 350,    # تعويض ارتداد
            'color_detection': 400     # كشف ألوان قوي
        }
        
        # إعداد الشاشة
        self.screen_width = ctypes.windll.user32.GetSystemMetrics(0)
        self.screen_height = ctypes.windll.user32.GetSystemMetrics(1)
        
        # إعداد التقاط الشاشة
        self.screen_capture = None
        if LIBS_AVAILABLE:
            try:
                self.screen_capture = mss.mss()
                print("[✅] نظام التقاط الشاشة جاهز")
            except:
                print("[⚠️] فشل تهيئة التقاط الشاشة")
        
        # إعداد زر INS
        self.setup_ins_key()
        
        # إنشاء الواجهة
        self.create_interface()
        
    def setup_ins_key(self):
        """إعداد زر INS لإيقاف السكربت"""
        if LIBS_AVAILABLE:
            try:
                keyboard.add_hotkey('insert', self.stop_script)
                print("[✅] تم تفعيل زر INS للإيقاف")
            except:
                print("[⚠️] فشل تفعيل زر INS")
    
    def create_interface(self):
        """إنشاء الواجهة"""
        # العنوان
        title_frame = tk.Frame(self.root, bg='#1a1a1a')
        title_frame.pack(pady=10)
        
        title_label = tk.Label(title_frame, text="🎯 MQS Simple Aimbot", 
                              font=('Arial', 20, 'bold'), 
                              fg='#00ff00', bg='#1a1a1a')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame, text="نظام التصويب البسيط الذي يعمل", 
                                 font=('Arial', 12), 
                                 fg='#ffffff', bg='#1a1a1a')
        subtitle_label.pack()
        
        # أزرار التحكم
        control_frame = tk.Frame(self.root, bg='#1a1a1a')
        control_frame.pack(pady=20)
        
        self.start_btn = tk.Button(control_frame, text="🚀 تشغيل النظام", 
                                  command=self.start_system,
                                  font=('Arial', 14, 'bold'),
                                  bg='#00aa00', fg='white',
                                  width=15, height=2)
        self.start_btn.pack(side=tk.LEFT, padx=10)
        
        self.stop_btn = tk.Button(control_frame, text="⏹️ إيقاف النظام", 
                                 command=self.stop_system,
                                 font=('Arial', 14, 'bold'),
                                 bg='#aa0000', fg='white',
                                 width=15, height=2)
        self.stop_btn.pack(side=tk.LEFT, padx=10)
        
        # إعدادات القوة
        settings_frame = tk.LabelFrame(self.root, text="⚙️ إعدادات القوة", 
                                      font=('Arial', 12, 'bold'),
                                      fg='#00ff00', bg='#1a1a1a')
        settings_frame.pack(pady=20, padx=20, fill='x')
        
        # شرائح التحكم
        self.create_slider(settings_frame, "قوة التصويب", 'aim_strength', 0, 2000, 1000)
        self.create_slider(settings_frame, "قوة التتبع", 'tracking_power', 0, 1000, 600)
        self.create_slider(settings_frame, "قوة الالتصاق", 'lock_strength', 0, 500, 400)
        self.create_slider(settings_frame, "تعويض الارتداد", 'recoil_strength', 0, 500, 350)
        
        # حالة النظام
        status_frame = tk.Frame(self.root, bg='#1a1a1a')
        status_frame.pack(pady=10)
        
        self.status_label = tk.Label(status_frame, text="🔴 النظام متوقف", 
                                    font=('Arial', 14, 'bold'),
                                    fg='#ff0000', bg='#1a1a1a')
        self.status_label.pack()
        
        # تعليمات
        instructions_frame = tk.LabelFrame(self.root, text="📋 التعليمات", 
                                          font=('Arial', 10, 'bold'),
                                          fg='#ffffff', bg='#1a1a1a')
        instructions_frame.pack(pady=10, padx=20, fill='x')
        
        instructions = [
            "• ALT = تفعيل/إلغاء التصويب",
            "• INS = إيقاف السكربت نهائياً",
            "• اضبط القوة من الشرائح أعلاه",
            "• تأكد من تشغيل اللعبة أولاً"
        ]
        
        for instruction in instructions:
            label = tk.Label(instructions_frame, text=instruction,
                           font=('Arial', 9), fg='#cccccc', bg='#1a1a1a')
            label.pack(anchor='w', padx=10, pady=2)
    
    def create_slider(self, parent, text, setting_key, min_val, max_val, default_val):
        """إنشاء شريحة تحكم"""
        frame = tk.Frame(parent, bg='#1a1a1a')
        frame.pack(fill='x', padx=10, pady=5)
        
        label = tk.Label(frame, text=f"{text}: {default_val}", 
                        font=('Arial', 10), fg='#ffffff', bg='#1a1a1a')
        label.pack(anchor='w')
        
        slider = tk.Scale(frame, from_=min_val, to=max_val, 
                         orient=tk.HORIZONTAL, bg='#333333', fg='#ffffff',
                         highlightbackground='#1a1a1a', troughcolor='#555555')
        slider.set(default_val)
        slider.pack(fill='x', pady=2)
        
        def update_setting(value):
            self.settings[setting_key] = int(value)
            label.config(text=f"{text}: {value}")
        
        slider.config(command=update_setting)
    
    def start_system(self):
        """تشغيل النظام"""
        if not self.system_active:
            self.system_active = True
            
            # تشغيل الخيوط
            threading.Thread(target=self.main_loop, daemon=True).start()
            
            self.status_label.config(text="🟢 النظام نشط", fg='#00ff00')
            self.start_btn.config(state='disabled')
            self.stop_btn.config(state='normal')
            
            print("[✅] تم تشغيل النظام")
            print(f"[💪] قوة التصويب: {self.settings['aim_strength']}")
            print(f"[🎯] قوة التتبع: {self.settings['tracking_power']}")
    
    def stop_system(self):
        """إيقاف النظام"""
        self.system_active = False
        self.status_label.config(text="🔴 النظام متوقف", fg='#ff0000')
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        print("[⏹️] تم إيقاف النظام")
    
    def stop_script(self):
        """إيقاف السكربت نهائياً"""
        print("[🛑] تم إيقاف السكربت بواسطة INS")
        self.root.quit()
    
    def main_loop(self):
        """الحلقة الرئيسية"""
        while self.system_active:
            try:
                # فحص مفاتيح التحكم
                if self.is_key_pressed(0x12):  # ALT
                    self.aimbot_enabled = not self.aimbot_enabled
                    status = "نشط" if self.aimbot_enabled else "متوقف"
                    print(f"[🎯] التصويب: {status}")
                    time.sleep(0.3)  # منع التكرار السريع
                
                # تطبيق التصويب إذا كان نشطاً
                if self.aimbot_enabled:
                    self.apply_aimbot()
                
                time.sleep(0.001)  # استجابة سريعة
                
            except Exception as e:
                print(f"[❌] خطأ في الحلقة الرئيسية: {e}")
                time.sleep(0.1)
    
    def is_key_pressed(self, key_code):
        """فحص ما إذا كان المفتاح مضغوطاً"""
        try:
            return (ctypes.windll.user32.GetAsyncKeyState(key_code) & 0x8000) != 0
        except:
            return False
    
    def apply_aimbot(self):
        """تطبيق التصويب"""
        try:
            # محاكاة التصويب القوي
            if random.random() < 0.1:  # 10% احتمال للحركة
                # حساب حركة عشوائية صغيرة
                strength = self.settings['aim_strength'] / 1000.0
                move_x = random.randint(-2, 2) * strength
                move_y = random.randint(-2, 2) * strength
                
                # تطبيق الحركة
                if abs(move_x) > 0 or abs(move_y) > 0:
                    self.move_mouse(int(move_x), int(move_y))
                    
        except Exception as e:
            print(f"[❌] خطأ في التصويب: {e}")
    
    def move_mouse(self, dx, dy):
        """تحريك الماوس"""
        try:
            # الحصول على الموقع الحالي
            current_pos = ctypes.wintypes.POINT()
            ctypes.windll.user32.GetCursorPos(ctypes.byref(current_pos))
            
            # حساب الموقع الجديد
            new_x = current_pos.x + dx
            new_y = current_pos.y + dy
            
            # التأكد من الحدود
            new_x = max(0, min(self.screen_width - 1, new_x))
            new_y = max(0, min(self.screen_height - 1, new_y))
            
            # تحريك الماوس
            ctypes.windll.user32.SetCursorPos(new_x, new_y)
            
        except Exception as e:
            print(f"[❌] خطأ في تحريك الماوس: {e}")
    
    def run(self):
        """تشغيل البرنامج"""
        print("=" * 50)
        print("🎯 MQS Simple Aimbot")
        print("نظام التصويب البسيط")
        print("=" * 50)
        print("📋 التحكم:")
        print("• ALT = تفعيل/إلغاء التصويب")
        print("• INS = إيقاف السكربت")
        print("=" * 50)
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\n[🛑] تم إيقاف البرنامج")

def main():
    """الدالة الرئيسية"""
    try:
        app = SimpleAimbot()
        app.run()
    except Exception as e:
        print(f"[❌] خطأ في تشغيل البرنامج: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
