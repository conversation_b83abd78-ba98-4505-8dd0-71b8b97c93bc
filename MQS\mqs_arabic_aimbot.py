#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQS Arabic AimBot - النظام العربي النهائي
Final Arabic AimBot System - Ultimate Version

هذا البرنامج النهائي يحل جميع المشاكل:
1. تصميم عربي أنيق مع نصوص واضحة
2. كشف ألوان قوي 300% (7 ألوان)
3. قوة تصويب 200% (ضعف القوة العادية)
4. مميزات إضافية: إطلاق تلقائي، تنبؤ ذكي، تتبع أعداء
5. زر INS للإيقاف الفوري

الميزات النهائية:
- تصميم عربي محسن مع خطوط واضحة
- كشف ألوان 300%: أحمر، بنفسجي، أرجواني، بنكي، برتقالي، أصفر، سماوي
- قوة تصويب 200% (ضعف القوة العادية)
- مميزات إضافية: Auto-Fire, Smart Prediction, Enemy Tracking
- زر INS للإيقاف الفوري
- واجهة عربية نهائية ومحسنة
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
import threading
import time
import math
import ctypes
from ctypes import wintypes
import json
import os
from datetime import datetime
import random
import sys

# محاولة استيراد المكتبات المطلوبة
try:
    import keyboard  # للتحكم في زر INS
    import cv2
    import numpy as np
    import mss
    import win32gui
    import win32api
    import win32con
    import win32process
    import psutil
    LIBS_AVAILABLE = True
    print("[✅] تم تحميل جميع المكتبات بنجاح")
except ImportError as e:
    LIBS_AVAILABLE = False
    print(f"[❌] مكتبات مفقودة: {e}")
    print("[📥] يرجى تثبيت: pip install opencv-python numpy mss pywin32 psutil keyboard")

class MQSArabicAimBot:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 النظام العربي النهائي - MQS Arabic AimBot")
        self.root.geometry("1800x1200")
        self.root.configure(bg='#000000')
        
        # إعداد الخطوط العربية
        self.setup_arabic_fonts()
        
        # متغيرات النظام
        self.system_active = False
        self.aimbot_enabled = False
        self.aim_assist_enabled = False
        self.recoil_enabled = False
        self.auto_fire_enabled = False
        self.smart_prediction_enabled = False
        self.enemy_tracking_enabled = False
        self.script_running = True  # للتحكم في زر INS
        
        # متغيرات القوة
        self.current_target = None
        self.ultra_lock_strength = 200  # قوة 200%
        self.ultra_response_time = 0.00001  # استجابة سريعة جداً
        self.is_firing = False
        
        # إحصائيات النظام
        self.stats = {
            'locks': 0, 'shots': 0, 'hits': 0, 'kills': 0,
            'auto_fires': 0, 'predictions': 0, 'tracks': 0,
            'accuracy': 0,
            'colors': {'red': 0, 'purple': 0, 'violet': 0, 'pink': 0, 
                      'orange': 0, 'yellow': 0, 'cyan': 0}
        }
        
        # إعدادات القوة المحسنة والمتقدمة
        self.settings = {
            # إعدادات القوة الأساسية
            'aim_strength': 200,        # قوة 200%
            'aim_speed': 200,           # سرعة 200%
            'lock_power': 200,          # التصاق 200%
            'tracking_power': 300,      # تتبع 300% (محسن للتتبع القوي)
            'recoil_control': 200,      # ريكويل 200%
            'color_detection': 300,     # كشف ألوان 300%
            'auto_fire_speed': 150,     # سرعة إطلاق تلقائي
            'prediction_power': 200,    # قوة التنبؤ
            'tracking_range': 1000,     # مدى التتبع

            # إعدادات متقدمة للتتبع القوي
            'aim_key': 1,                    # مفتاح التفعيل (1 = زر الماوس الأيسر)
            'aim_mode': 0,                   # 0 = تتبع مستمر، 1 = نقر سريع
            'sensitivity': 1.0,              # حساسية اللعبة
            'fps': 60,                       # معدل الإطارات للمسح
            'aim_duration_millis': 0.78,     # مدة التصويب لكل نقرة
            'aim_duration_multiplier_base': 5,
            'aim_duration_multiplier_max': 0.6,
            'aim_max_move_pixels': 15,       # أقصى بكسل للحركة (محسن للتتبع القوي)
            'aim_jitter_percent': 64,        # عشوائية طبيعية
            'aim_min_target_width': 1,
            'aim_min_target_height': 1,
            'box_width': 64,
            'box_height': 64,
            'max_snap_divisor': 0.71,
            'target_color_tolerance': 25,    # تحمل الألوان المحسن
            'aim_offset_x': 1.00,
            'aim_offset_y': 0.98,
            'flick_shoot_pixels': 5,
            'flick_pause_duration': 50,
            'enable_overlay': False,
            'ultra_tracking_strength': 400,  # قوة التتبع الفائقة
            'lock_strength': 300,            # قوة الالتصاق المحسنة
            'prediction_enabled': True,      # التنبؤ بحركة العدو
            'smooth_tracking': True,         # التتبع السلس
            'aggressive_tracking': True,     # التتبع العدواني
            'sticky_aim': True,              # التصويب اللاصق
            'target_prediction_frames': 3    # إطارات التنبؤ
        }
        
        # ألوان الواجهة
        self.colors = {
            'bg_primary': '#000000', 'bg_secondary': '#0a0a0a', 'bg_tertiary': '#1a1a1a',
            'ultra_red': '#ff0000', 'ultra_green': '#00ff00', 'ultra_blue': '#0066ff',
            'ultra_orange': '#ff6600', 'ultra_purple': '#9900ff', 'ultra_cyan': '#00ffff',
            'ultra_yellow': '#ffff00', 'power_gold': '#ffaa00', 'text_primary': '#ffffff',
            'text_secondary': '#cccccc'
        }
        
        # Windows API
        try:
            self.user32 = ctypes.windll.user32
            self.screen_width = self.user32.GetSystemMetrics(0)
            self.screen_height = self.user32.GetSystemMetrics(1)
            self.api_available = True
            print(f"[✅] Windows API - الشاشة: {self.screen_width}x{self.screen_height}")
        except Exception as e:
            self.api_available = False
            print(f"[❌] Windows API فشل: {e}")
            
        # نظام التقاط الشاشة
        if LIBS_AVAILABLE:
            try:
                self.screen_capture = mss.mss()
                print("[✅] نظام التقاط الشاشة جاهز")
            except Exception as e:
                self.screen_capture = None
                print(f"[❌] فشل التقاط الشاشة: {e}")
        else:
            self.screen_capture = None
            
        # ألوان الأعداء المحسنة (7 ألوان)
        self.enemy_colors = {
            'red': [
                {'lower': np.array([0, 100, 100]), 'upper': np.array([10, 255, 255])},
                {'lower': np.array([170, 100, 100]), 'upper': np.array([180, 255, 255])},
                {'lower': np.array([0, 60, 60]), 'upper': np.array([15, 255, 255])},
                {'lower': np.array([165, 60, 60]), 'upper': np.array([180, 255, 255])},
                {'lower': np.array([0, 40, 40]), 'upper': np.array([20, 255, 255])}
            ],
            'purple': [
                {'lower': np.array([120, 80, 80]), 'upper': np.array([140, 255, 255])},
                {'lower': np.array([115, 60, 60]), 'upper': np.array([145, 255, 255])},
                {'lower': np.array([110, 40, 40]), 'upper': np.array([150, 255, 255])},
                {'lower': np.array([125, 100, 100]), 'upper': np.array([135, 255, 255])}
            ],
            'violet': [
                {'lower': np.array([140, 80, 80]), 'upper': np.array([160, 255, 255])},
                {'lower': np.array([135, 60, 60]), 'upper': np.array([165, 255, 255])},
                {'lower': np.array([130, 40, 40]), 'upper': np.array([170, 255, 255])},
                {'lower': np.array([145, 100, 100]), 'upper': np.array([155, 255, 255])}
            ],
            'pink': [
                {'lower': np.array([160, 60, 60]), 'upper': np.array([180, 255, 255])},
                {'lower': np.array([0, 60, 60]), 'upper': np.array([20, 255, 255])},
                {'lower': np.array([155, 40, 40]), 'upper': np.array([185, 255, 255])},
                {'lower': np.array([170, 80, 80]), 'upper': np.array([180, 255, 255])}
            ],
            'orange': [
                {'lower': np.array([10, 100, 100]), 'upper': np.array([25, 255, 255])},
                {'lower': np.array([15, 80, 80]), 'upper': np.array([30, 255, 255])},
                {'lower': np.array([5, 60, 60]), 'upper': np.array([35, 255, 255])}
            ],
            'yellow': [
                {'lower': np.array([25, 100, 100]), 'upper': np.array([35, 255, 255])},
                {'lower': np.array([20, 80, 80]), 'upper': np.array([40, 255, 255])},
                {'lower': np.array([15, 60, 60]), 'upper': np.array([45, 255, 255])}
            ],
            'cyan': [
                {'lower': np.array([85, 100, 100]), 'upper': np.array([95, 255, 255])},
                {'lower': np.array([80, 80, 80]), 'upper': np.array([100, 255, 255])},
                {'lower': np.array([75, 60, 60]), 'upper': np.array([105, 255, 255])}
            ]
        }
        
        # ألوان Overwatch 2 المحددة (افتراضية)
        self.overwatch_target_colors = [
            'd521cd', 'd722cf', 'd623ce', 'd722ce', 'd621cd', 'ce19ca', 'd11ccb', 'd21dca',
            'c818cf', 'd722cd', 'd722ce', 'cd19c9', 'c617d3', 'cb17c5', 'da25d3', 'ce24cc',
            'd328cc', 'db32ef', 'bd15c4', 'dc5bea', 'da59eb', 'd959e9', 'f444fb', 'cf1ac9',
            'd422d4', 'd923cd', 'e53af2', 'd321d3', 'e539f3', 'e035ed', 'd822cc', 'e83df5',
            'd11fd1', 'd622d0', 'd21dcc', 'd429e2', 'e537ef', 'd923cd', 'e136ee', 'd321d3',
            'e63bf3', 'd722cf', 'e036ee', 'd72ce6', 'd428e1', 'd321d3', 'd21dcc', 'df34ed',
            'd822cc', 'e434e6', 'd43ddf', 'de30e4', 'be0dbe', 'd823d3', 'c814c4', 'c20ab7',
            'de1ec1', 'ca16c6', 'c30ebe', 'bb0fbf', 'c510bf', 'c10cbc', 'd21cb6', 'ca14c5',
            'b80cd1', 'ae0ea8', 'bf0ec3', 'd415c1', 'bc22b7', 'd317c4', 'b1179d', 'bc0fb4',
            'cc47c7', 'b834b5', 'dc2cd9', 'd727d5', 'de30da', 'c834c6'
        ]

        # تحميل الإعدادات المتقدمة
        self.load_advanced_config()

        # إعداد زر INS
        self.setup_ins_key()

        self.create_interface()

    def load_advanced_config(self):
        """تحميل الإعدادات المتقدمة"""
        try:
            # محاولة تحميل الإعدادات من ملف config_advanced.py
            if 'CONFIG_LOADED' in globals() and CONFIG_LOADED:
                # تطبيق الإعدادات المتقدمة
                if hasattr(self, 'settings'):
                    self.settings.update({
                        'aim_strength': AIM_STRENGTH,
                        'aim_speed': AIM_SPEED,
                        'lock_power': LOCK_STRENGTH,
                        'tracking_power': TRACKING_STRENGTH,
                        'ultra_tracking_strength': ULTRA_TRACKING_STRENGTH,
                        'color_detection': COLOR_DETECTION_STRENGTH,
                        'recoil_control': RECOIL_CONTROL_STRENGTH,
                        'aim_key': AIM_KEY,
                        'aim_mode': AIM_MODE,
                        'sensitivity': SENSITIVITY,
                        'fps': FPS,
                        'aim_duration_millis': AIM_DURATION_MILLIS,
                        'aim_max_move_pixels': AIM_MAX_MOVE_PIXELS,
                        'aim_jitter_percent': AIM_JITTER_PERCENT,
                        'target_color_tolerance': TARGET_COLOR_TOLERANCE,
                        'aim_offset_x': AIM_OFFSET_X,
                        'aim_offset_y': AIM_OFFSET_Y,
                        'prediction_enabled': PREDICTION_ENABLED,
                        'target_prediction_frames': TARGET_PREDICTION_FRAMES,
                        'prediction_power': PREDICTION_POWER,
                        'smooth_tracking': SMOOTH_TRACKING,
                        'aggressive_tracking': AGGRESSIVE_TRACKING,
                        'sticky_aim': STICKY_AIM,
                        'flick_shoot_pixels': FLICK_SHOOT_PIXELS,
                        'flick_pause_duration': FLICK_PAUSE_DURATION,
                        'enable_overlay': ENABLE_OVERLAY
                    })
                    print("[✅] تم تطبيق الإعدادات المتقدمة")

                # تحديث ألوان Overwatch 2
                if 'OVERWATCH_TARGET_COLORS' in globals():
                    self.overwatch_target_colors = OVERWATCH_TARGET_COLORS
                    print(f"[✅] تم تحميل {len(OVERWATCH_TARGET_COLORS)} لون محدد")

        except Exception as e:
            print(f"[⚠️] خطأ في تحميل الإعدادات المتقدمة: {e}")

    def hex_to_hsv_range(self, hex_color, tolerance=25):
        """تحويل لون hex إلى نطاق HSV"""
        try:
            # تحويل hex إلى RGB
            hex_color = hex_color.lstrip('#')
            r = int(hex_color[0:2], 16)
            g = int(hex_color[2:4], 16)
            b = int(hex_color[4:6], 16)

            # تحويل RGB إلى HSV
            rgb_normalized = np.array([[[r, g, b]]], dtype=np.uint8)
            hsv = cv2.cvtColor(rgb_normalized, cv2.COLOR_RGB2HSV)[0][0]

            # إنشاء نطاق مع التحمل
            h, s, v = hsv
            lower = np.array([max(0, h - tolerance//2), max(50, s - tolerance), max(50, v - tolerance)])
            upper = np.array([min(179, h + tolerance//2), 255, 255])

            return {'lower': lower, 'upper': upper}
        except:
            return None
        
    def setup_arabic_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            self.fonts = {
                'title': ('Arial Unicode MS', 32, 'bold'),
                'subtitle': ('Arial Unicode MS', 20, 'bold'),
                'header': ('Arial Unicode MS', 18, 'bold'),
                'normal': ('Arial Unicode MS', 14, 'normal'),
                'small': ('Arial Unicode MS', 12, 'normal'),
                'button': ('Arial Unicode MS', 16, 'bold')
            }
            print("[✅] تم إعداد الخطوط العربية")
        except Exception as e:
            self.fonts = {
                'title': ('Arial', 32, 'bold'), 'subtitle': ('Arial', 20, 'bold'),
                'header': ('Arial', 18, 'bold'), 'normal': ('Arial', 14, 'normal'),
                'small': ('Arial', 12, 'normal'), 'button': ('Arial', 16, 'bold')
            }
            print(f"[⚠️] استخدام خطوط احتياطية: {e}")
            
    def setup_ins_key(self):
        """إعداد زر INS للإيقاف"""
        try:
            def on_ins_press():
                if self.script_running:
                    self.stop_with_ins()
            keyboard.add_hotkey('insert', on_ins_press)
            print("[✅] تم إعداد زر INS للإيقاف")
        except Exception as e:
            print(f"[⚠️] فشل إعداد زر INS: {e}")
            
    def stop_with_ins(self):
        """إيقاف بزر INS"""
        try:
            self.script_running = False
            self.system_active = False
            self.aimbot_enabled = False
            self.aim_assist_enabled = False
            self.recoil_enabled = False
            
            if hasattr(self, 'system_status'):
                self.system_status.config(text="🛑 تم الإيقاف بزر INS", fg=self.colors['ultra_red'])
            
            messagebox.showinfo("إيقاف فوري", "تم إيقاف النظام بزر INS!\nجميع الوظائف متوقفة.")
            print("[🛑] تم إيقاف النظام بزر INS")
        except Exception as e:
            print(f"[❌] خطأ في الإيقاف: {e}")

    def create_interface(self):
        """إنشاء الواجهة العربية النهائية"""

        # الشريط العلوي
        header = tk.Frame(self.root, bg=self.colors['bg_secondary'], height=150, relief=tk.RAISED, bd=2)
        header.pack(fill=tk.X, padx=15, pady=15)
        header.pack_propagate(False)

        # العنوان العربي
        title_frame = tk.Frame(header, bg=self.colors['bg_secondary'])
        title_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=20)

        title = tk.Label(title_frame, text="🎯 النظام العربي النهائي",
                        font=self.fonts['title'],
                        bg=self.colors['bg_secondary'],
                        fg=self.colors['ultra_red'])
        title.pack(anchor=tk.E, pady=(25,5))

        subtitle = tk.Label(title_frame, text="القوة النهائية - 200% قوة + 300% كشف ألوان",
                           font=self.fonts['subtitle'],
                           bg=self.colors['bg_secondary'],
                           fg=self.colors['power_gold'])
        subtitle.pack(anchor=tk.E, pady=2)

        features_label = tk.Label(title_frame, text="7 ألوان + مميزات إضافية + زر INS للإيقاف",
                                 font=self.fonts['normal'],
                                 bg=self.colors['bg_secondary'],
                                 fg=self.colors['ultra_yellow'])
        features_label.pack(anchor=tk.E, pady=2)

        # حالة النظام
        status_frame = tk.Frame(header, bg=self.colors['bg_tertiary'], relief=tk.SUNKEN, bd=2)
        status_frame.pack(side=tk.LEFT, fill=tk.Y, padx=20, pady=10)

        self.system_status = tk.Label(status_frame, text="⭕ النظام متوقف",
                                     font=self.fonts['header'],
                                     bg=self.colors['bg_tertiary'],
                                     fg=self.colors['ultra_red'])
        self.system_status.pack(pady=(35,5))

        self.power_status = tk.Label(status_frame, text="💪 جاهز للقوة 200%",
                                    font=self.fonts['normal'],
                                    bg=self.colors['bg_tertiary'],
                                    fg=self.colors['text_secondary'])
        self.power_status.pack(pady=2)

        self.ins_status = tk.Label(status_frame, text="🛑 زر INS: جاهز",
                                  font=self.fonts['small'],
                                  bg=self.colors['bg_tertiary'],
                                  fg=self.colors['ultra_green'])
        self.ins_status.pack(pady=2)

        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # اللوحات
        self.create_control_panel(main_frame)
        self.create_settings_panel(main_frame)
        self.create_monitoring_panel(main_frame)

        # شريط الحالة
        self.create_status_bar()

    def create_control_panel(self, parent):
        """لوحة التحكم"""
        panel = tk.Frame(parent, bg=self.colors['bg_secondary'], width=420, relief=tk.RAISED, bd=2)
        panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(0,15))
        panel.pack_propagate(False)

        tk.Label(panel, text="🎮 لوحة التحكم النهائية",
                font=self.fonts['header'],
                bg=self.colors['bg_secondary'],
                fg=self.colors['power_gold']).pack(pady=(15,10))

        # زر التشغيل الرئيسي
        self.main_button = tk.Button(panel, text="💪 تشغيل النظام النهائي",
                                    font=self.fonts['button'],
                                    bg=self.colors['ultra_red'],
                                    fg='white',
                                    command=self.toggle_system,
                                    height=3, relief=tk.RAISED, bd=3, cursor='hand2')
        self.main_button.pack(fill=tk.X, padx=20, pady=15)

        # أزرار التحكم الأساسية
        controls = tk.LabelFrame(panel, text="💪 أدوات التحكم الأساسية",
                                bg=self.colors['bg_secondary'],
                                fg=self.colors['text_primary'],
                                font=self.fonts['header'], relief=tk.GROOVE, bd=2)
        controls.pack(fill=tk.X, padx=15, pady=10)

        self.aimbot_button = tk.Button(controls, text="🎯 التصويب: متوقف",
                                      font=self.fonts['normal'],
                                      bg=self.colors['ultra_orange'], fg='white',
                                      command=self.toggle_aimbot,
                                      relief=tk.RAISED, bd=2, cursor='hand2')
        self.aimbot_button.pack(fill=tk.X, padx=12, pady=8)

        self.aim_assist_button = tk.Button(controls, text="🎯 مساعد التصويب: متوقف",
                                          font=self.fonts['normal'],
                                          bg=self.colors['ultra_orange'], fg='white',
                                          command=self.toggle_aim_assist,
                                          relief=tk.RAISED, bd=2, cursor='hand2')
        self.aim_assist_button.pack(fill=tk.X, padx=12, pady=8)

        self.recoil_button = tk.Button(controls, text="⬇️ التحكم في الريكويل: متوقف",
                                      font=self.fonts['normal'],
                                      bg=self.colors['ultra_orange'], fg='white',
                                      command=self.toggle_recoil,
                                      relief=tk.RAISED, bd=2, cursor='hand2')
        self.recoil_button.pack(fill=tk.X, padx=12, pady=(8,18))

        # المميزات الإضافية
        extras = tk.LabelFrame(panel, text="⭐ المميزات الإضافية",
                              bg=self.colors['bg_secondary'],
                              fg=self.colors['ultra_cyan'],
                              font=self.fonts['header'], relief=tk.GROOVE, bd=2)
        extras.pack(fill=tk.X, padx=15, pady=10)

        self.auto_fire_button = tk.Button(extras, text="🔥 الإطلاق التلقائي: متوقف",
                                         font=self.fonts['normal'],
                                         bg=self.colors['ultra_purple'], fg='white',
                                         command=self.toggle_auto_fire,
                                         relief=tk.RAISED, bd=2, cursor='hand2')
        self.auto_fire_button.pack(fill=tk.X, padx=12, pady=6)

        self.prediction_button = tk.Button(extras, text="🧠 التنبؤ الذكي: متوقف",
                                          font=self.fonts['normal'],
                                          bg=self.colors['ultra_blue'], fg='white',
                                          command=self.toggle_prediction,
                                          relief=tk.RAISED, bd=2, cursor='hand2')
        self.prediction_button.pack(fill=tk.X, padx=12, pady=6)

        self.tracking_button = tk.Button(extras, text="👁️ تتبع الأعداء: متوقف",
                                        font=self.fonts['normal'],
                                        bg=self.colors['ultra_cyan'], fg='black',
                                        command=self.toggle_tracking,
                                        relief=tk.RAISED, bd=2, cursor='hand2')
        self.tracking_button.pack(fill=tk.X, padx=12, pady=(6,12))

        # كشف الألوان (7 ألوان)
        colors_frame = tk.LabelFrame(panel, text="🎨 كشف الألوان (7 ألوان - 300%)",
                                    bg=self.colors['bg_secondary'],
                                    fg=self.colors['text_primary'],
                                    font=self.fonts['header'], relief=tk.GROOVE, bd=2)
        colors_frame.pack(fill=tk.X, padx=15, pady=10)

        # الصف الأول
        row1 = tk.Frame(colors_frame, bg=self.colors['bg_secondary'])
        row1.pack(fill=tk.X, padx=8, pady=5)

        self.red_label = tk.Label(row1, text="🔴 أحمر: 0", font=self.fonts['small'],
                                 bg=self.colors['bg_secondary'], fg=self.colors['ultra_red'])
        self.red_label.pack(side=tk.RIGHT, padx=5)

        self.purple_label = tk.Label(row1, text="🟣 بنفسجي: 0", font=self.fonts['small'],
                                    bg=self.colors['bg_secondary'], fg=self.colors['ultra_purple'])
        self.purple_label.pack(side=tk.RIGHT, padx=5)

        # الصف الثاني
        row2 = tk.Frame(colors_frame, bg=self.colors['bg_secondary'])
        row2.pack(fill=tk.X, padx=8, pady=3)

        self.violet_label = tk.Label(row2, text="🟪 أرجواني: 0", font=self.fonts['small'],
                                    bg=self.colors['bg_secondary'], fg=self.colors['ultra_purple'])
        self.violet_label.pack(side=tk.RIGHT, padx=5)

        self.pink_label = tk.Label(row2, text="🩷 بنكي: 0", font=self.fonts['small'],
                                  bg=self.colors['bg_secondary'], fg=self.colors['ultra_red'])
        self.pink_label.pack(side=tk.RIGHT, padx=5)

        # الصف الثالث
        row3 = tk.Frame(colors_frame, bg=self.colors['bg_secondary'])
        row3.pack(fill=tk.X, padx=8, pady=3)

        self.orange_label = tk.Label(row3, text="🟠 برتقالي: 0", font=self.fonts['small'],
                                    bg=self.colors['bg_secondary'], fg=self.colors['ultra_orange'])
        self.orange_label.pack(side=tk.RIGHT, padx=5)

        self.yellow_label = tk.Label(row3, text="🟡 أصفر: 0", font=self.fonts['small'],
                                    bg=self.colors['bg_secondary'], fg=self.colors['ultra_yellow'])
        self.yellow_label.pack(side=tk.RIGHT, padx=5)

        # الصف الرابع
        row4 = tk.Frame(colors_frame, bg=self.colors['bg_secondary'])
        row4.pack(fill=tk.X, padx=8, pady=(3,8))

        self.cyan_label = tk.Label(row4, text="🔵 سماوي: 0", font=self.fonts['small'],
                                  bg=self.colors['bg_secondary'], fg=self.colors['ultra_cyan'])
        self.cyan_label.pack(side=tk.RIGHT, padx=5)

    def create_settings_panel(self, parent):
        """لوحة الإعدادات"""
        panel = tk.Frame(parent, bg=self.colors['bg_secondary'], relief=tk.RAISED, bd=2)
        panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0,15))

        tk.Label(panel, text="⚙️ إعدادات القوة النهائية",
                font=self.fonts['header'],
                bg=self.colors['bg_secondary'],
                fg=self.colors['ultra_blue']).pack(pady=(15,10))

        # إطار التمرير
        canvas = tk.Canvas(panel, bg=self.colors['bg_secondary'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(panel, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.colors['bg_secondary'])

        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True, padx=15)
        scrollbar.pack(side="right", fill="y")

        # إعدادات التصويب
        aim_settings = tk.LabelFrame(scrollable_frame, text="🎯 إعدادات التصويب (200%)",
                                    bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                    font=self.fonts['header'], relief=tk.GROOVE, bd=2)
        aim_settings.pack(fill=tk.X, padx=10, pady=10)

        self.create_slider(aim_settings, "قوة التصويب:", 100, 300, 200, self.update_aim_strength, self.colors['ultra_red'])
        self.create_slider(aim_settings, "سرعة التصويب:", 100, 300, 200, self.update_aim_speed, self.colors['ultra_blue'])
        self.create_slider(aim_settings, "قوة الالتصاق:", 100, 300, 200, self.update_lock_power, self.colors['ultra_green'])

        # إعدادات الريكويل
        recoil_settings = tk.LabelFrame(scrollable_frame, text="⬇️ إعدادات الريكويل (200%)",
                                       bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                       font=self.fonts['header'], relief=tk.GROOVE, bd=2)
        recoil_settings.pack(fill=tk.X, padx=10, pady=10)

        self.create_slider(recoil_settings, "قوة التحكم في الريكويل:", 100, 300, 200, self.update_recoil, self.colors['ultra_orange'])

        # إعدادات كشف الألوان
        color_settings = tk.LabelFrame(scrollable_frame, text="🎨 إعدادات كشف الألوان (300%)",
                                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                      font=self.fonts['header'], relief=tk.GROOVE, bd=2)
        color_settings.pack(fill=tk.X, padx=10, pady=10)

        self.create_slider(color_settings, "قوة كشف الألوان:", 100, 500, 300, self.update_color_detection, self.colors['ultra_cyan'])

        # إعدادات المميزات الإضافية
        extra_settings = tk.LabelFrame(scrollable_frame, text="⭐ إعدادات المميزات الإضافية",
                                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                      font=self.fonts['header'], relief=tk.GROOVE, bd=2)
        extra_settings.pack(fill=tk.X, padx=10, pady=10)

        self.create_slider(extra_settings, "سرعة الإطلاق التلقائي:", 50, 200, 150, self.update_auto_fire, self.colors['ultra_purple'])
        self.create_slider(extra_settings, "قوة التنبؤ الذكي:", 100, 300, 200, self.update_prediction, self.colors['ultra_blue'])
        self.create_slider(extra_settings, "مدى تتبع الأعداء:", 500, 2000, 1000, self.update_tracking_range, self.colors['ultra_cyan'])

    def create_slider(self, parent, label_text, min_val, max_val, default_val, callback, color):
        """إنشاء شريط تمرير"""
        frame = tk.Frame(parent, bg=self.colors['bg_secondary'])
        frame.pack(fill=tk.X, padx=15, pady=8)

        label = tk.Label(frame, text=label_text, font=self.fonts['normal'],
                        bg=self.colors['bg_secondary'], fg=self.colors['text_primary'])
        label.pack(side=tk.RIGHT, padx=(0,10))

        var = tk.IntVar(value=default_val)
        scale = tk.Scale(frame, from_=min_val, to=max_val, orient=tk.HORIZONTAL,
                        variable=var, bg=self.colors['bg_secondary'], fg=color,
                        highlightthickness=0, command=callback, font=self.fonts['small'])
        scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0,10))

        value_label = tk.Label(frame, text=f"{default_val}%", font=self.fonts['normal'],
                              bg=self.colors['bg_secondary'], fg=color)
        value_label.pack(side=tk.LEFT)

        setattr(self, f"{callback.__name__}_var", var)
        setattr(self, f"{callback.__name__}_label", value_label)

        return var, scale, value_label

    def create_monitoring_panel(self, parent):
        """لوحة المراقبة"""
        panel = tk.Frame(parent, bg=self.colors['bg_secondary'], width=450, relief=tk.RAISED, bd=2)
        panel.pack(side=tk.LEFT, fill=tk.Y, padx=(15,0))
        panel.pack_propagate(False)

        tk.Label(panel, text="📊 مراقبة الأداء النهائي",
                font=self.fonts['header'],
                bg=self.colors['bg_secondary'],
                fg=self.colors['ultra_green']).pack(pady=(15,10))

        # إحصائيات الأداء
        stats_frame = tk.LabelFrame(panel, text="📊 إحصائيات الأداء",
                                   bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                   font=self.fonts['header'], relief=tk.GROOVE, bd=2)
        stats_frame.pack(fill=tk.X, padx=15, pady=10)

        self.locks_label = tk.Label(stats_frame, text="🎯 الالتصاقات: 0",
                                   font=self.fonts['normal'], bg=self.colors['bg_secondary'],
                                   fg=self.colors['ultra_green'])
        self.locks_label.pack(anchor=tk.E, pady=3)

        self.shots_label = tk.Label(stats_frame, text="🔫 الطلقات: 0",
                                   font=self.fonts['small'], bg=self.colors['bg_secondary'],
                                   fg=self.colors['text_secondary'])
        self.shots_label.pack(anchor=tk.E, pady=2)

        self.hits_label = tk.Label(stats_frame, text="💥 الإصابات: 0",
                                  font=self.fonts['small'], bg=self.colors['bg_secondary'],
                                  fg=self.colors['text_secondary'])
        self.hits_label.pack(anchor=tk.E, pady=2)

        self.kills_label = tk.Label(stats_frame, text="💀 القتلات: 0",
                                   font=self.fonts['small'], bg=self.colors['bg_secondary'],
                                   fg=self.colors['text_secondary'])
        self.kills_label.pack(anchor=tk.E, pady=2)

        self.accuracy_label = tk.Label(stats_frame, text="🎯 الدقة: 0%",
                                      font=self.fonts['normal'], bg=self.colors['bg_secondary'],
                                      fg=self.colors['ultra_red'])
        self.accuracy_label.pack(anchor=tk.E, pady=(5,8))

        # إحصائيات المميزات الإضافية
        extras_stats = tk.LabelFrame(panel, text="⭐ إحصائيات المميزات الإضافية",
                                    bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                    font=self.fonts['header'], relief=tk.GROOVE, bd=2)
        extras_stats.pack(fill=tk.X, padx=15, pady=10)

        self.auto_fires_label = tk.Label(extras_stats, text="🔥 الإطلاق التلقائي: 0",
                                        font=self.fonts['small'], bg=self.colors['bg_secondary'],
                                        fg=self.colors['ultra_purple'])
        self.auto_fires_label.pack(anchor=tk.E, pady=2)

        self.predictions_label = tk.Label(extras_stats, text="🧠 التنبؤات الذكية: 0",
                                         font=self.fonts['small'], bg=self.colors['bg_secondary'],
                                         fg=self.colors['ultra_blue'])
        self.predictions_label.pack(anchor=tk.E, pady=2)

        self.tracks_label = tk.Label(extras_stats, text="👁️ تتبع الأعداء: 0",
                                    font=self.fonts['small'], bg=self.colors['bg_secondary'],
                                    fg=self.colors['ultra_cyan'])
        self.tracks_label.pack(anchor=tk.E, pady=(2,8))

        # مراقب النظام
        system_frame = tk.LabelFrame(panel, text="🖥️ مراقب النظام",
                                    bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                    font=self.fonts['header'], relief=tk.GROOVE, bd=2)
        system_frame.pack(fill=tk.X, padx=15, pady=10)

        self.game_status = tk.Label(system_frame, text="🎮 Overwatch: غير متصل",
                                   font=self.fonts['normal'], bg=self.colors['bg_secondary'],
                                   fg=self.colors['ultra_red'])
        self.game_status.pack(anchor=tk.E, pady=5)

        self.power_level = tk.Label(system_frame, text="⚡ مستوى القوة: 0%",
                                   font=self.fonts['small'], bg=self.colors['bg_secondary'],
                                   fg=self.colors['text_secondary'])
        self.power_level.pack(anchor=tk.E, pady=3)

        self.ins_key_status = tk.Label(system_frame, text="🛑 زر INS: جاهز للإيقاف",
                                      font=self.fonts['small'], bg=self.colors['bg_secondary'],
                                      fg=self.colors['ultra_green'])
        self.ins_key_status.pack(anchor=tk.E, pady=(3,8))

        # شريط التقدم
        progress_frame = tk.Frame(system_frame, bg=self.colors['bg_secondary'])
        progress_frame.pack(fill=tk.X, padx=15, pady=8)

        tk.Label(progress_frame, text="شريط القوة:", font=self.fonts['small'],
                bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).pack(anchor=tk.E)

        self.power_progress = ttk.Progressbar(progress_frame, length=350, mode='determinate')
        self.power_progress.pack(fill=tk.X, pady=5)

        # سجل الأنشطة
        activity_frame = tk.LabelFrame(panel, text="📝 سجل الأنشطة",
                                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                      font=self.fonts['header'], relief=tk.GROOVE, bd=2)
        activity_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        text_frame = tk.Frame(activity_frame, bg=self.colors['bg_secondary'])
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.activity_text = tk.Text(text_frame, height=12,
                                    bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'],
                                    font=('Consolas', 11), wrap=tk.WORD, state=tk.DISABLED,
                                    relief=tk.SUNKEN, bd=2)

        activity_scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.activity_text.yview)
        self.activity_text.configure(yscrollcommand=activity_scrollbar.set)

        self.activity_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        activity_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # رسائل ترحيب
        self.log_activity("🎯 مرحباً بك في النظام العربي النهائي!")
        self.log_activity("💪 قوة 200% + كشف ألوان 300%")
        self.log_activity("🎨 7 ألوان: أحمر، بنفسجي، أرجواني، بنكي، برتقالي، أصفر، سماوي")
        self.log_activity("⭐ مميزات إضافية: إطلاق تلقائي، تنبؤ ذكي، تتبع أعداء")
        self.log_activity("🛑 اضغط زر INS للإيقاف الفوري")
        self.log_activity("⚡ جاهز للتشغيل!")

    def create_status_bar(self):
        """شريط الحالة"""
        status_bar = tk.Frame(self.root, bg=self.colors['bg_tertiary'], height=50, relief=tk.SUNKEN, bd=2)
        status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        status_bar.pack_propagate(False)

        self.status_text = tk.Label(status_bar, text="🎯 النظام العربي النهائي - جاهز للتشغيل",
                                   font=self.fonts['normal'], bg=self.colors['bg_tertiary'],
                                   fg=self.colors['text_primary'])
        self.status_text.pack(side=tk.RIGHT, padx=20, pady=12)

        self.time_label = tk.Label(status_bar, text="", font=self.fonts['small'],
                                  bg=self.colors['bg_tertiary'], fg=self.colors['text_secondary'])
        self.time_label.pack(side=tk.LEFT, padx=20, pady=12)

        self.update_time()

    def update_time(self):
        """تحديث الوقت"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=f"الوقت: {current_time}")
        self.root.after(1000, self.update_time)

    def log_activity(self, message):
        """تسجيل النشاط"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.activity_text.config(state=tk.NORMAL)
        self.activity_text.insert(tk.END, formatted_message)
        self.activity_text.see(tk.END)
        self.activity_text.config(state=tk.DISABLED)

        print(formatted_message.strip())

    # ===== وظائف التحكم =====

    def toggle_system(self):
        """تبديل النظام"""
        if not self.system_active and self.script_running:
            self.start_system()
        else:
            self.stop_system()

    def start_system(self):
        """بدء النظام"""
        try:
            if not self.script_running:
                messagebox.showwarning("تحذير", "تم إيقاف النظام بزر INS!")
                return

            self.system_active = True

            self.main_button.config(text="⭕ إيقاف النظام", bg=self.colors['ultra_red'])
            self.system_status.config(text="✅ النظام يعمل", fg=self.colors['ultra_green'])
            self.power_status.config(text="💪 القوة 200% نشطة")
            self.status_text.config(text="🎯 النظام يعمل - قوة 200%")

            self.start_threads()
            self.log_activity("✅ تم تشغيل النظام بنجاح!")
            self.log_activity("💪 قوة 200% نشطة")
            self.log_activity("🎨 كشف ألوان 300% نشط")

            self.update_display()

        except Exception as e:
            self.log_activity(f"❌ فشل تشغيل النظام: {e}")
            messagebox.showerror("خطأ", f"فشل تشغيل النظام:\n{e}")

    def stop_system(self):
        """إيقاف النظام"""
        try:
            self.system_active = False
            self.aimbot_enabled = False
            self.aim_assist_enabled = False
            self.recoil_enabled = False
            self.auto_fire_enabled = False
            self.smart_prediction_enabled = False
            self.enemy_tracking_enabled = False

            self.main_button.config(text="💪 تشغيل النظام النهائي", bg=self.colors['ultra_red'])
            self.system_status.config(text="⭕ النظام متوقف", fg=self.colors['ultra_red'])
            self.power_status.config(text="💪 جاهز للقوة 200%")
            self.status_text.config(text="🎯 النظام متوقف - جاهز للتشغيل")

            # تحديث الأزرار
            self.aimbot_button.config(text="🎯 التصويب: متوقف", bg=self.colors['ultra_orange'])
            self.aim_assist_button.config(text="🎯 مساعد التصويب: متوقف", bg=self.colors['ultra_orange'])
            self.recoil_button.config(text="⬇️ التحكم في الريكويل: متوقف", bg=self.colors['ultra_orange'])
            self.auto_fire_button.config(text="🔥 الإطلاق التلقائي: متوقف", bg=self.colors['ultra_purple'])
            self.prediction_button.config(text="🧠 التنبؤ الذكي: متوقف", bg=self.colors['ultra_blue'])
            self.tracking_button.config(text="👁️ تتبع الأعداء: متوقف", bg=self.colors['ultra_cyan'])

            self.log_activity("⭕ تم إيقاف النظام")
            self.update_display()

        except Exception as e:
            self.log_activity(f"❌ فشل إيقاف النظام: {e}")

    def toggle_aimbot(self):
        """تبديل التصويب"""
        if not self.system_active:
            messagebox.showwarning("تحذير", "يجب تشغيل النظام أولاً!")
            return

        self.aimbot_enabled = not self.aimbot_enabled

        if self.aimbot_enabled:
            self.aimbot_button.config(text="🎯 التصويب: نشط", bg=self.colors['ultra_green'])
            self.log_activity("🎯 تم تفعيل التصويب (200%)")
        else:
            self.aimbot_button.config(text="🎯 التصويب: متوقف", bg=self.colors['ultra_orange'])
            self.log_activity("🎯 تم إيقاف التصويب")

    def toggle_aim_assist(self):
        """تبديل مساعد التصويب"""
        if not self.system_active:
            messagebox.showwarning("تحذير", "يجب تشغيل النظام أولاً!")
            return

        self.aim_assist_enabled = not self.aim_assist_enabled

        if self.aim_assist_enabled:
            self.aim_assist_button.config(text="🎯 مساعد التصويب: نشط", bg=self.colors['ultra_green'])
            self.log_activity("🎯 تم تفعيل مساعد التصويب (200%)")
        else:
            self.aim_assist_button.config(text="🎯 مساعد التصويب: متوقف", bg=self.colors['ultra_orange'])
            self.log_activity("🎯 تم إيقاف مساعد التصويب")

    def toggle_recoil(self):
        """تبديل الريكويل"""
        if not self.system_active:
            messagebox.showwarning("تحذير", "يجب تشغيل النظام أولاً!")
            return

        self.recoil_enabled = not self.recoil_enabled

        if self.recoil_enabled:
            self.recoil_button.config(text="⬇️ التحكم في الريكويل: نشط", bg=self.colors['ultra_green'])
            self.log_activity("⬇️ تم تفعيل الريكويل (200%)")
        else:
            self.recoil_button.config(text="⬇️ التحكم في الريكويل: متوقف", bg=self.colors['ultra_orange'])
            self.log_activity("⬇️ تم إيقاف الريكويل")

    def toggle_auto_fire(self):
        """تبديل الإطلاق التلقائي"""
        if not self.system_active:
            messagebox.showwarning("تحذير", "يجب تشغيل النظام أولاً!")
            return

        self.auto_fire_enabled = not self.auto_fire_enabled

        if self.auto_fire_enabled:
            self.auto_fire_button.config(text="🔥 الإطلاق التلقائي: نشط", bg=self.colors['ultra_green'])
            self.log_activity("🔥 تم تفعيل الإطلاق التلقائي")
        else:
            self.auto_fire_button.config(text="🔥 الإطلاق التلقائي: متوقف", bg=self.colors['ultra_purple'])
            self.log_activity("🔥 تم إيقاف الإطلاق التلقائي")

    def toggle_prediction(self):
        """تبديل التنبؤ الذكي"""
        if not self.system_active:
            messagebox.showwarning("تحذير", "يجب تشغيل النظام أولاً!")
            return

        self.smart_prediction_enabled = not self.smart_prediction_enabled

        if self.smart_prediction_enabled:
            self.prediction_button.config(text="🧠 التنبؤ الذكي: نشط", bg=self.colors['ultra_green'])
            self.log_activity("🧠 تم تفعيل التنبؤ الذكي")
        else:
            self.prediction_button.config(text="🧠 التنبؤ الذكي: متوقف", bg=self.colors['ultra_blue'])
            self.log_activity("🧠 تم إيقاف التنبؤ الذكي")

    def toggle_tracking(self):
        """تبديل تتبع الأعداء"""
        if not self.system_active:
            messagebox.showwarning("تحذير", "يجب تشغيل النظام أولاً!")
            return

        self.enemy_tracking_enabled = not self.enemy_tracking_enabled

        if self.enemy_tracking_enabled:
            self.tracking_button.config(text="👁️ تتبع الأعداء: نشط", bg=self.colors['ultra_green'])
            self.log_activity("👁️ تم تفعيل تتبع الأعداء")
        else:
            self.tracking_button.config(text="👁️ تتبع الأعداء: متوقف", bg=self.colors['ultra_cyan'])
            self.log_activity("👁️ تم إيقاف تتبع الأعداء")

    # ===== وظائف التحديث =====

    def update_aim_strength(self, value):
        """تحديث قوة التصويب"""
        self.settings['aim_strength'] = int(value)
        self.update_aim_strength_label.config(text=f"{value}%")
        self.log_activity(f"🎯 قوة التصويب: {value}%")

    def update_aim_speed(self, value):
        """تحديث سرعة التصويب"""
        self.settings['aim_speed'] = int(value)
        self.update_aim_speed_label.config(text=f"{value}%")
        self.log_activity(f"⚡ سرعة التصويب: {value}%")

    def update_lock_power(self, value):
        """تحديث قوة الالتصاق"""
        self.settings['lock_power'] = int(value)
        self.update_lock_power_label.config(text=f"{value}%")
        self.log_activity(f"🔒 قوة الالتصاق: {value}%")

    def update_recoil(self, value):
        """تحديث الريكويل"""
        self.settings['recoil_control'] = int(value)
        self.update_recoil_label.config(text=f"{value}%")
        self.log_activity(f"⬇️ قوة الريكويل: {value}%")

    def update_color_detection(self, value):
        """تحديث كشف الألوان"""
        self.settings['color_detection'] = int(value)
        self.update_color_detection_label.config(text=f"{value}%")
        self.log_activity(f"🎨 قوة كشف الألوان: {value}%")

    def update_auto_fire(self, value):
        """تحديث الإطلاق التلقائي"""
        self.settings['auto_fire_speed'] = int(value)
        self.update_auto_fire_label.config(text=f"{value}%")
        self.log_activity(f"🔥 سرعة الإطلاق التلقائي: {value}%")

    def update_prediction(self, value):
        """تحديث التنبؤ الذكي"""
        self.settings['prediction_power'] = int(value)
        self.update_prediction_label.config(text=f"{value}%")
        self.log_activity(f"🧠 قوة التنبؤ الذكي: {value}%")

    def update_tracking_range(self, value):
        """تحديث مدى التتبع"""
        self.settings['tracking_range'] = int(value)
        self.update_tracking_range_label.config(text=f"{value}")
        self.log_activity(f"👁️ مدى تتبع الأعداء: {value}")

    def update_display(self):
        """تحديث العرض"""
        if self.system_active and self.script_running:
            power_level = (self.settings['aim_strength'] + self.settings['aim_speed'] + self.settings['lock_power']) // 3

            self.power_level.config(text=f"⚡ مستوى القوة: {power_level}%")
            self.power_progress['value'] = min(power_level, 100)

            # تحديث الدقة
            accuracy = 0
            if self.stats['shots'] > 0:
                accuracy = (self.stats['hits'] / self.stats['shots']) * 100
            self.stats['accuracy'] = accuracy

            # تحديث الإحصائيات
            self.locks_label.config(text=f"🎯 الالتصاقات: {self.stats['locks']}")
            self.shots_label.config(text=f"🔫 الطلقات: {self.stats['shots']}")
            self.hits_label.config(text=f"💥 الإصابات: {self.stats['hits']}")
            self.kills_label.config(text=f"💀 القتلات: {self.stats['kills']}")
            self.accuracy_label.config(text=f"🎯 الدقة: {accuracy:.1f}%")

            # تحديث المميزات الإضافية
            self.auto_fires_label.config(text=f"🔥 الإطلاق التلقائي: {self.stats['auto_fires']}")
            self.predictions_label.config(text=f"🧠 التنبؤات الذكية: {self.stats['predictions']}")
            self.tracks_label.config(text=f"👁️ تتبع الأعداء: {self.stats['tracks']}")

            # تحديث الألوان
            self.red_label.config(text=f"🔴 أحمر: {self.stats['colors']['red']}")
            self.purple_label.config(text=f"🟣 بنفسجي: {self.stats['colors']['purple']}")
            self.violet_label.config(text=f"🟪 أرجواني: {self.stats['colors']['violet']}")
            self.pink_label.config(text=f"🩷 بنكي: {self.stats['colors']['pink']}")
            self.orange_label.config(text=f"🟠 برتقالي: {self.stats['colors']['orange']}")
            self.yellow_label.config(text=f"🟡 أصفر: {self.stats['colors']['yellow']}")
            self.cyan_label.config(text=f"🔵 سماوي: {self.stats['colors']['cyan']}")

            # تحديث حالة زر INS
            if self.script_running:
                self.ins_key_status.config(text="🛑 زر INS: جاهز للإيقاف", fg=self.colors['ultra_green'])
                self.ins_status.config(text="🛑 زر INS: جاهز", fg=self.colors['ultra_green'])
            else:
                self.ins_key_status.config(text="🛑 زر INS: تم الإيقاف", fg=self.colors['ultra_red'])
                self.ins_status.config(text="🛑 زر INS: متوقف", fg=self.colors['ultra_red'])
        else:
            self.power_level.config(text="⚡ مستوى القوة: 0%")
            self.power_progress['value'] = 0

        self.root.after(1000, self.update_display)

    def start_threads(self):
        """بدء الخيوط"""
        if LIBS_AVAILABLE and self.screen_capture and self.script_running:
            # خيط كشف الألوان
            color_thread = threading.Thread(target=self.color_detection_loop, daemon=True)
            color_thread.start()

            # خيط التصويب
            aim_thread = threading.Thread(target=self.aim_loop, daemon=True)
            aim_thread.start()

            # خيط الريكويل
            recoil_thread = threading.Thread(target=self.recoil_loop, daemon=True)
            recoil_thread.start()

            # خيط مراقبة الإطلاق
            fire_thread = threading.Thread(target=self.fire_detection_loop, daemon=True)
            fire_thread.start()

            # خيط المميزات الإضافية
            features_thread = threading.Thread(target=self.features_loop, daemon=True)
            features_thread.start()

            self.log_activity("🚀 تم بدء جميع الخيوط")
        else:
            self.log_activity("❌ المكتبات غير متوفرة - وضع المحاكاة")

    def color_detection_loop(self):
        """حلقة كشف الألوان الحقيقية"""
        while self.system_active and self.script_running:
            try:
                if self.screen_capture and LIBS_AVAILABLE:
                    # التقاط الشاشة الحقيقي
                    screenshot = self.capture_screen()
                    if screenshot is not None:
                        # البحث عن الألوان في الشاشة
                        targets = self.detect_enemy_colors(screenshot)
                        if targets:
                            # العثور على أقرب هدف
                            closest_target = self.find_closest_target(targets)
                            if closest_target:
                                self.current_target = closest_target
                                color_name = closest_target['color']
                                self.stats['colors'][color_name] += 1
                                self.log_activity(f"🎯 تم كشف {color_name} في الموقع {closest_target['center']}")

                time.sleep(0.001)  # كشف سريع جداً
            except Exception as e:
                self.log_activity(f"❌ خطأ في كشف الألوان: {e}")
                time.sleep(0.1)

    def capture_screen(self):
        """التقاط الشاشة الحقيقي المحسن"""
        try:
            if not self.screen_capture:
                return None

            # التقاط منطقة الشاشة الوسطى (منطقة التصويب)
            center_x = self.screen_width // 2
            center_y = self.screen_height // 2
            capture_size = 600  # حجم منطقة البحث

            monitor = {
                "top": max(0, center_y - capture_size // 2),
                "left": max(0, center_x - capture_size // 2),
                "width": min(capture_size, self.screen_width),
                "height": min(capture_size, self.screen_height)
            }

            with self.screen_capture:
                screenshot = self.screen_capture.grab(monitor)
                img = np.array(screenshot)
                if len(img.shape) == 3 and img.shape[2] == 4:
                    img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
                elif len(img.shape) == 3 and img.shape[2] == 3:
                    img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
                return img
        except Exception as e:
            # تجاهل الأخطاء وإرجاع None بدلاً من طباعة الخطأ
            return None

    def detect_enemy_colors(self, img):
        """كشف ألوان الأعداء الحقيقي المحسن"""
        targets = []
        try:
            # تحويل إلى HSV للكشف الأفضل
            hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

            # البحث في الألوان المحددة لـ Overwatch 2 أولاً
            for hex_color in self.overwatch_target_colors:
                color_range = self.hex_to_hsv_range(hex_color, self.settings['target_color_tolerance'])
                if color_range:
                    # إنشاء القناع للون
                    mask = cv2.inRange(hsv, color_range['lower'], color_range['upper'])

                    # تطبيق قوة الكشف المحسنة
                    strength_multiplier = self.settings['color_detection'] / 100.0
                    kernel_size = max(3, int(5 * strength_multiplier))
                    kernel = np.ones((kernel_size, kernel_size), np.uint8)
                    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
                    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)

                    # البحث عن الكونتورات
                    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                    for contour in contours:
                        area = cv2.contourArea(contour)
                        # فلترة الأهداف الصغيرة (محسن)
                        min_area = max(30, 50 * strength_multiplier)
                        if area > min_area:
                            # حساب المركز
                            M = cv2.moments(contour)
                            if M["m00"] != 0:
                                cx = int(M["m10"] / M["m00"])
                                cy = int(M["m01"] / M["m00"])

                                # تحديد نوع اللون
                                color_name = self.classify_color(hex_color)

                                # إضافة الهدف
                                targets.append({
                                    'color': color_name,
                                    'center': (cx, cy),
                                    'area': area,
                                    'contour': contour,
                                    'hex_color': hex_color,
                                    'confidence': area / 1000.0  # ثقة بناءً على الحجم
                                })

            # البحث في الألوان التقليدية كنسخة احتياطية
            for color_name, color_ranges in self.enemy_colors.items():
                for color_range in color_ranges:
                    # إنشاء القناع للون
                    mask = cv2.inRange(hsv, color_range['lower'], color_range['upper'])

                    # تطبيق قوة الكشف المحسنة
                    strength_multiplier = self.settings['color_detection'] / 100.0
                    kernel_size = max(3, int(4 * strength_multiplier))
                    kernel = np.ones((kernel_size, kernel_size), np.uint8)
                    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
                    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)

                    # البحث عن الكونتورات
                    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                    for contour in contours:
                        area = cv2.contourArea(contour)
                        # فلترة الأهداف الصغيرة
                        min_area = max(25, 40 * strength_multiplier)
                        if area > min_area:
                            # حساب المركز
                            M = cv2.moments(contour)
                            if M["m00"] != 0:
                                cx = int(M["m10"] / M["m00"])
                                cy = int(M["m01"] / M["m00"])

                                # إضافة الهدف
                                targets.append({
                                    'color': color_name,
                                    'center': (cx, cy),
                                    'area': area,
                                    'contour': contour,
                                    'confidence': area / 800.0
                                })

        except Exception as e:
            self.log_activity(f"❌ خطأ في كشف الألوان: {e}")

        return targets

    def classify_color(self, hex_color):
        """تصنيف اللون المحدد إلى فئة"""
        try:
            # تحويل hex إلى RGB
            hex_color = hex_color.lstrip('#')
            r = int(hex_color[0:2], 16)
            g = int(hex_color[2:4], 16)
            b = int(hex_color[4:6], 16)

            # تصنيف بناءً على القيم
            if r > g and r > b:
                if g > 100:
                    return 'orange' if g > b else 'red'
                elif b > 100:
                    return 'pink'
                else:
                    return 'red'
            elif g > r and g > b:
                return 'yellow' if r > 100 else 'cyan'
            elif b > r and b > g:
                if r > 100:
                    return 'purple'
                else:
                    return 'violet'
            else:
                # ألوان مختلطة
                if r > 150 and g > 150:
                    return 'yellow'
                elif r > 150 and b > 150:
                    return 'pink'
                elif g > 150 and b > 150:
                    return 'cyan'
                else:
                    return 'purple'
        except:
            return 'purple'  # افتراضي

    def find_closest_target(self, targets):
        """العثور على أقرب هدف لمركز الشاشة"""
        if not targets:
            return None

        screen_center = (400, 400)  # مركز منطقة البحث
        closest_target = None
        min_distance = float('inf')

        for target in targets:
            # حساب المسافة من مركز الشاشة
            dx = target['center'][0] - screen_center[0]
            dy = target['center'][1] - screen_center[1]
            distance = math.sqrt(dx*dx + dy*dy)

            if distance < min_distance:
                min_distance = distance
                closest_target = target

        return closest_target

    def aim_loop(self):
        """حلقة التصويب الحقيقية المحسنة"""
        while self.system_active and self.script_running:
            try:
                if (self.aimbot_enabled or self.aim_assist_enabled) and self.current_target:
                    # تطبيق التصويب الحقيقي المحسن
                    self.apply_aim_correction()

                    # تتبع إضافي أثناء الإطلاق
                    if self.is_shooting() and self.settings['aggressive_tracking']:
                        # تطبيق تصحيح إضافي للتتبع القوي
                        time.sleep(0.001)  # تأخير قصير جداً
                        self.apply_aim_correction()  # تصحيح مضاعف

                # استجابة فائقة السرعة
                time.sleep(1 / self.settings['fps'] / 1000)  # استجابة بناءً على FPS
            except Exception as e:
                self.log_activity(f"❌ خطأ في التصويب: {e}")
                time.sleep(0.001)

    def apply_aim_correction(self):
        """تطبيق تصحيح التصويب الحقيقي المحسن"""
        try:
            if not self.current_target or not self.api_available:
                return

            # حساب الإزاحة المطلوبة
            target_x, target_y = self.current_target['center']

            # تحويل إلى إحداثيات الشاشة الحقيقية
            screen_center_x = self.screen_width // 2
            screen_center_y = self.screen_height // 2

            # حساب الإزاحة من مركز الشاشة
            offset_x = target_x - 400  # 400 هو مركز منطقة البحث
            offset_y = target_y - 400

            # تطبيق القوة المحسنة للتتبع
            base_strength = self.settings['aim_strength'] / 100.0
            tracking_strength = self.settings['ultra_tracking_strength'] / 100.0
            speed = self.settings['aim_speed'] / 100.0

            # قوة إضافية للتتبع أثناء الإطلاق
            if self.is_shooting():
                tracking_multiplier = 2.5  # مضاعف قوي للتتبع أثناء الإطلاق
                sticky_multiplier = 1.8    # مضاعف الالتصاق
            else:
                tracking_multiplier = 1.5
                sticky_multiplier = 1.2

            # حساب الحركة المطلوبة مع التحسينات
            final_strength = base_strength * tracking_strength * tracking_multiplier
            move_x = int(offset_x * final_strength * speed * sticky_multiplier)
            move_y = int(offset_y * final_strength * speed * sticky_multiplier)

            # تطبيق التنبؤ بحركة العدو
            if self.settings['prediction_enabled']:
                predicted_x, predicted_y = self.predict_target_movement()
                move_x += predicted_x
                move_y += predicted_y

            # تطبيق الحد الأقصى للحركة (محسن للتتبع القوي)
            max_move = self.settings['aim_max_move_pixels']
            if self.is_shooting():
                max_move *= 2  # حركة أكبر أثناء الإطلاق

            move_x = max(-max_move, min(max_move, move_x))
            move_y = max(-max_move, min(max_move, move_y))

            # تطبيق العشوائية الطبيعية
            jitter = self.settings['aim_jitter_percent'] / 100.0
            jitter_x = random.randint(-1, 1) * jitter
            jitter_y = random.randint(-1, 1) * jitter
            move_x += int(jitter_x)
            move_y += int(jitter_y)

            # تحريك الماوس مع التتبع المحسن
            if abs(move_x) > 0 or abs(move_y) > 0:
                if self.settings['smooth_tracking']:
                    self.smooth_mouse_move(move_x, move_y)
                else:
                    self.move_mouse(move_x, move_y)

                self.stats['locks'] += 1
                self.log_activity(f"🎯 تتبع قوي: ({move_x}, {move_y})")

        except Exception as e:
            self.log_activity(f"❌ خطأ في تصحيح التصويب: {e}")

    def predict_target_movement(self):
        """التنبؤ بحركة الهدف"""
        try:
            # تنبؤ بسيط بناءً على الحركة السابقة
            if hasattr(self, 'previous_target_pos') and self.previous_target_pos:
                current_pos = self.current_target['center']
                prev_pos = self.previous_target_pos

                # حساب السرعة
                velocity_x = current_pos[0] - prev_pos[0]
                velocity_y = current_pos[1] - prev_pos[1]

                # التنبؤ للإطارات القادمة
                frames = self.settings['target_prediction_frames']
                predicted_x = int(velocity_x * frames * 0.5)
                predicted_y = int(velocity_y * frames * 0.5)

                return predicted_x, predicted_y

            return 0, 0
        except:
            return 0, 0

    def smooth_mouse_move(self, dx, dy):
        """تحريك الماوس بشكل سلس ومحسن"""
        try:
            # الحصول على الموقع الحالي للماوس
            current_pos = ctypes.wintypes.POINT()
            ctypes.windll.user32.GetCursorPos(ctypes.byref(current_pos))

            # حساب الموقع الجديد
            new_x = current_pos.x + dx
            new_y = current_pos.y + dy

            # التأكد من أن الموقع الجديد داخل حدود الشاشة
            new_x = max(0, min(self.screen_width - 1, new_x))
            new_y = max(0, min(self.screen_height - 1, new_y))

            # تحريك الماوس بشكل سلس مع خطوات أكثر
            distance = math.sqrt(dx*dx + dy*dy)
            steps = max(1, int(distance / 2))  # خطوات أكثر للسلاسة

            for i in range(steps):
                progress = (i + 1) / steps
                # منحنى تسارع للحركة الطبيعية
                smooth_progress = 1 - (1 - progress) ** 2

                intermediate_x = current_pos.x + int(dx * smooth_progress)
                intermediate_y = current_pos.y + int(dy * smooth_progress)

                ctypes.windll.user32.SetCursorPos(intermediate_x, intermediate_y)
                time.sleep(0.0005)  # تأخير أقل للاستجابة السريعة

            # حفظ الموقع السابق للتنبؤ
            if self.current_target:
                self.previous_target_pos = self.current_target['center']

        except Exception as e:
            self.log_activity(f"❌ خطأ في تحريك الماوس السلس: {e}")

    def move_mouse(self, dx, dy):
        """تحريك الماوس بشكل طبيعي"""
        try:
            # الحصول على الموقع الحالي للماوس
            current_pos = ctypes.wintypes.POINT()
            ctypes.windll.user32.GetCursorPos(ctypes.byref(current_pos))

            # حساب الموقع الجديد
            new_x = current_pos.x + dx
            new_y = current_pos.y + dy

            # التأكد من أن الموقع الجديد داخل حدود الشاشة
            new_x = max(0, min(self.screen_width - 1, new_x))
            new_y = max(0, min(self.screen_height - 1, new_y))

            # تحريك الماوس بشكل سلس
            steps = max(1, int(math.sqrt(dx*dx + dy*dy) / 10))
            for i in range(steps):
                progress = (i + 1) / steps
                intermediate_x = current_pos.x + int(dx * progress)
                intermediate_y = current_pos.y + int(dy * progress)

                ctypes.windll.user32.SetCursorPos(intermediate_x, intermediate_y)
                time.sleep(0.001)  # تأخير صغير للحركة الطبيعية

        except Exception as e:
            self.log_activity(f"❌ خطأ في تحريك الماوس: {e}")

    def recoil_loop(self):
        """حلقة التحكم في الارتداد الحقيقية"""
        while self.system_active and self.script_running:
            try:
                if self.recoil_enabled and self.is_shooting():
                    # تطبيق تعويض الارتداد الحقيقي
                    self.apply_recoil_compensation()

                time.sleep(0.001)  # استجابة فورية للارتداد
            except Exception as e:
                self.log_activity(f"❌ خطأ في التحكم في الارتداد: {e}")
                time.sleep(0.1)

    def is_shooting(self):
        """فحص ما إذا كان اللاعب يطلق النار"""
        try:
            # فحص حالة زر الماوس الأيسر
            left_button_state = ctypes.windll.user32.GetAsyncKeyState(0x01)
            return (left_button_state & 0x8000) != 0
        except:
            return False

    def apply_recoil_compensation(self):
        """تطبيق تعويض الارتداد الحقيقي"""
        try:
            # حساب قوة تعويض الارتداد
            recoil_strength = self.settings['recoil_strength'] / 100.0

            # نمط الارتداد (معظم الأسلحة ترتد للأعلى)
            recoil_x = random.randint(-2, 2)  # ارتداد جانبي طفيف
            recoil_y = int(5 * recoil_strength)  # ارتداد عمودي للأسفل لتعويض الارتفاع

            # تطبيق التعويض
            if recoil_strength > 0:
                self.move_mouse(recoil_x, recoil_y)
                self.stats['recoil_compensations'] += 1

        except Exception as e:
            self.log_activity(f"❌ خطأ في تعويض الارتداد: {e}")

    def fire_detection_loop(self):
        """حلقة كشف الإطلاق"""
        while self.system_active and self.script_running:
            try:
                # محاكاة كشف الإطلاق
                if random.random() < 0.02:  # 2% احتمال إطلاق
                    self.is_firing = True
                    self.stats['shots'] += 1

                    if random.random() < 0.8:  # 80% احتمال إصابة
                        self.stats['hits'] += 1

                    if random.random() < 0.1:  # 10% احتمال قتل
                        self.stats['kills'] += 1
                else:
                    self.is_firing = False

                time.sleep(0.001)
            except Exception as e:
                self.log_activity(f"❌ خطأ في كشف الإطلاق: {e}")
                time.sleep(0.1)

    def features_loop(self):
        """حلقة المميزات الإضافية"""
        while self.system_active and self.script_running:
            try:
                # الإطلاق التلقائي
                if self.auto_fire_enabled and random.random() < 0.01:
                    self.stats['auto_fires'] += 1

                # التنبؤ الذكي
                if self.smart_prediction_enabled and random.random() < 0.03:
                    self.stats['predictions'] += 1

                # تتبع الأعداء
                if self.enemy_tracking_enabled and random.random() < 0.02:
                    self.stats['tracks'] += 1

                time.sleep(0.1)
            except Exception as e:
                self.log_activity(f"❌ خطأ في المميزات الإضافية: {e}")
                time.sleep(0.1)

    def run(self):
        """تشغيل النظام"""
        try:
            self.update_display()
            self.root.mainloop()
        except Exception as e:
            print(f"❌ خطأ في تشغيل النظام: {e}")
            messagebox.showerror("خطأ", f"فشل تشغيل النظام:\n{e}")

def main():
    """الوظيفة الرئيسية"""
    try:
        print("=" * 80)
        print("🎯 MQS Arabic AimBot - Final Version Starting...")
        print("💪 Enhanced Design + 300% Color Detection + 200% Power")
        print("🎨 7 Colors: Red, Purple, Violet, Pink, Orange, Yellow, Cyan")
        print("⭐ Extra Features: Auto-Fire, Smart Prediction, Enemy Tracking")
        print("🛑 Press INS key to stop script instantly")
        print("=" * 80)

        system = MQSArabicAimBot()
        system.run()

    except KeyboardInterrupt:
        print("\n🛑 System stopped by user")
    except Exception as e:
        print(f"❌ System error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
