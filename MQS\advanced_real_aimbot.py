#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQS Advanced Real Aimbot - Professional Overwatch 2 Integration
نظام التصويب المتقدم الحقيقي - تكامل احترافي مع أوفرواتش 2
"""

import cv2
import numpy as np
import mss
import time
import threading
import ctypes
import ctypes.wintypes
from ctypes import wintypes
import keyboard
import math
import random
import win32api
import win32con

class AdvancedRealAimbot:
    def __init__(self):
        print("🎯 MQS Advanced Real Aimbot - Professional Edition")
        print("=" * 70)
        
        # حالة النظام
        self.running = False
        self.aimbot_active = False
        self.target_locked = False
        self.last_target = None
        
        # إعدادات القوة المتقدمة
        self.settings = {
            'aim_strength': 2.5,        # قوة التصويب الأساسية
            'tracking_speed': 1.2,      # سرعة التتبع
            'lock_strength': 3.0,       # قوة الالتصاق
            'smoothing': 0.4,           # نعومة الحركة
            'prediction': 1.5,          # تنبؤ بالحركة
            'reaction_time': 0.05,      # زمن رد الفعل
            'max_distance': 400,        # أقصى مسافة للتتبع
            'min_target_size': 30,      # أصغر حجم هدف
            'headshot_bias': 0.8,       # تحيز نحو الرأس
        }
        
        # إعدادات الشاشة
        self.screen_width = ctypes.windll.user32.GetSystemMetrics(0)
        self.screen_height = ctypes.windll.user32.GetSystemMetrics(1)
        self.center_x = self.screen_width // 2
        self.center_y = self.screen_height // 2
        
        # منطقة البحث المتكيفة
        self.search_radius = 350
        self.dynamic_radius = True
        
        # إعداد التقاط الشاشة المحسن
        self.sct = mss.mss()
        
        # ألوان Overwatch 2 المحسنة (HSV)
        self.enemy_colors = {
            'red': [
                ([0, 100, 100], [10, 255, 255]),
                ([170, 100, 100], [180, 255, 255]),
                ([0, 80, 80], [15, 255, 255]),
                ([165, 80, 80], [180, 255, 255]),
            ],
            'purple': [
                ([130, 80, 80], [160, 255, 255]),
                ([140, 100, 100], [170, 255, 255]),
            ],
            'pink': [
                ([160, 80, 80], [180, 255, 255]),
                ([0, 80, 80], [10, 255, 255]),
            ],
            'orange': [
                ([10, 100, 100], [25, 255, 255]),
                ([15, 80, 80], [30, 255, 255]),
            ]
        }
        
        # إحصائيات الأداء
        self.stats = {
            'targets_detected': 0,
            'successful_locks': 0,
            'frames_processed': 0,
            'avg_fps': 0,
            'start_time': time.time()
        }
        
        # إعداد المفاتيح المتقدمة
        self.setup_advanced_hotkeys()
        
        print("✅ تم تهيئة النظام المتقدم")
        print("📋 التحكم المتقدم:")
        print("  ALT     = تفعيل/إلغاء التصويب")
        print("  CTRL+ALT = وضع التتبع القوي")
        print("  F1      = زيادة القوة")
        print("  F2      = تقليل القوة")
        print("  F3      = تغيير وضع التتبع")
        print("  F4      = إعادة ضبط الإعدادات")
        print("  INS     = إيقاف السكربت")
        print("=" * 70)
    
    def setup_advanced_hotkeys(self):
        """إعداد المفاتيح الساخنة المتقدمة"""
        try:
            keyboard.add_hotkey('alt', self.toggle_aimbot)
            keyboard.add_hotkey('ctrl+alt', self.toggle_strong_mode)
            keyboard.add_hotkey('insert', self.stop_script)
            keyboard.add_hotkey('f1', self.increase_strength)
            keyboard.add_hotkey('f2', self.decrease_strength)
            keyboard.add_hotkey('f3', self.cycle_tracking_mode)
            keyboard.add_hotkey('f4', self.reset_settings)
            print("✅ تم تفعيل المفاتيح المتقدمة")
        except Exception as e:
            print(f"⚠️ خطأ في المفاتيح: {e}")
    
    def toggle_aimbot(self):
        """تفعيل/إلغاء التصويب"""
        self.aimbot_active = not self.aimbot_active
        status = "نشط 🟢" if self.aimbot_active else "متوقف 🔴"
        print(f"🎯 التصويب: {status}")
        
        if self.aimbot_active:
            print(f"💪 القوة: {self.settings['aim_strength']:.1f}")
            print(f"🎯 التتبع: {self.settings['tracking_speed']:.1f}")
    
    def toggle_strong_mode(self):
        """تفعيل الوضع القوي"""
        if self.aimbot_active:
            # مضاعفة القوة مؤقتاً
            self.settings['aim_strength'] *= 1.5
            self.settings['tracking_speed'] *= 1.3
            self.settings['lock_strength'] *= 1.2
            print("🔥 الوضع القوي نشط!")
            
            # إعادة ضبط بعد 3 ثوان
            threading.Timer(3.0, self.reset_strong_mode).start()
    
    def reset_strong_mode(self):
        """إعادة ضبط الوضع القوي"""
        self.settings['aim_strength'] = min(3.0, self.settings['aim_strength'] / 1.5)
        self.settings['tracking_speed'] = min(2.0, self.settings['tracking_speed'] / 1.3)
        self.settings['lock_strength'] = min(4.0, self.settings['lock_strength'] / 1.2)
        print("⚡ انتهى الوضع القوي")
    
    def increase_strength(self):
        """زيادة قوة التصويب"""
        self.settings['aim_strength'] = min(5.0, self.settings['aim_strength'] + 0.3)
        print(f"⬆️ قوة التصويب: {self.settings['aim_strength']:.1f}")
    
    def decrease_strength(self):
        """تقليل قوة التصويب"""
        self.settings['aim_strength'] = max(0.5, self.settings['aim_strength'] - 0.3)
        print(f"⬇️ قوة التصويب: {self.settings['aim_strength']:.1f}")
    
    def cycle_tracking_mode(self):
        """تغيير وضع التتبع"""
        modes = [
            ('عادي', {'tracking_speed': 1.0, 'smoothing': 0.4}),
            ('سريع', {'tracking_speed': 1.5, 'smoothing': 0.2}),
            ('دقيق', {'tracking_speed': 0.8, 'smoothing': 0.6}),
            ('قوي', {'tracking_speed': 2.0, 'smoothing': 0.1})
        ]
        
        # العثور على الوضع الحالي والانتقال للتالي
        current_speed = self.settings['tracking_speed']
        next_mode = modes[0]  # افتراضي
        
        for i, (name, settings) in enumerate(modes):
            if abs(settings['tracking_speed'] - current_speed) < 0.1:
                next_mode = modes[(i + 1) % len(modes)]
                break
        
        # تطبيق الوضع الجديد
        mode_name, mode_settings = next_mode
        self.settings.update(mode_settings)
        print(f"🔄 وضع التتبع: {mode_name}")
    
    def reset_settings(self):
        """إعادة ضبط الإعدادات"""
        self.settings.update({
            'aim_strength': 2.5,
            'tracking_speed': 1.2,
            'lock_strength': 3.0,
            'smoothing': 0.4,
        })
        print("🔄 تم إعادة ضبط الإعدادات")
    
    def stop_script(self):
        """إيقاف السكربت"""
        print("🛑 إيقاف السكربت...")
        self.running = False
    
    def capture_screen_optimized(self):
        """التقاط الشاشة المحسن"""
        try:
            # تكييف منطقة البحث حسب النشاط
            if self.dynamic_radius:
                if self.target_locked:
                    radius = min(self.search_radius, 250)  # منطقة أصغر عند الالتصاق
                else:
                    radius = self.search_radius
            else:
                radius = self.search_radius
            
            # تحديد منطقة البحث
            left = max(0, self.center_x - radius)
            top = max(0, self.center_y - radius)
            width = min(radius * 2, self.screen_width - left)
            height = min(radius * 2, self.screen_height - top)
            
            monitor = {
                "top": top,
                "left": left,
                "width": width,
                "height": height
            }
            
            # التقاط الشاشة
            screenshot = self.sct.grab(monitor)
            img = np.array(screenshot)
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
            
            return img, (left, top)
            
        except Exception as e:
            print(f"❌ خطأ في التقاط الشاشة: {e}")
            return None, None
    
    def detect_enemies_advanced(self, img, offset):
        """كشف الأعداء المتقدم"""
        if img is None:
            return []
        
        try:
            # تحويل إلى HSV
            hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
            
            # تطبيق تنعيم للتقليل من الضوضاء
            hsv = cv2.GaussianBlur(hsv, (3, 3), 0)
            
            targets = []
            
            # البحث في جميع الألوان
            for color_name, color_ranges in self.enemy_colors.items():
                mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
                
                for lower, upper in color_ranges:
                    lower = np.array(lower)
                    upper = np.array(upper)
                    color_mask = cv2.inRange(hsv, lower, upper)
                    mask = cv2.bitwise_or(mask, color_mask)
                
                # تنظيف القناع
                kernel = np.ones((2, 2), np.uint8)
                mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
                mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
                
                # العثور على الكونتورات
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                for contour in contours:
                    area = cv2.contourArea(contour)
                    
                    # التحقق من الحد الأدنى للمساحة
                    if area > self.settings['min_target_size']:
                        # حساب المركز
                        M = cv2.moments(contour)
                        if M["m00"] != 0:
                            cx = int(M["m10"] / M["m00"])
                            cy = int(M["m01"] / M["m00"])
                            
                            # تحويل إلى إحداثيات الشاشة
                            screen_x = offset[0] + cx
                            screen_y = offset[1] + cy
                            
                            # حساب المسافة من المركز
                            distance = math.sqrt((screen_x - self.center_x)**2 + (screen_y - self.center_y)**2)
                            
                            # التحقق من المسافة القصوى
                            if distance <= self.settings['max_distance']:
                                # حساب الأولوية (أقرب = أولوية أعلى)
                                priority = (self.settings['max_distance'] - distance) / self.settings['max_distance']
                                priority *= (area / 1000.0)  # تعديل حسب الحجم
                                
                                targets.append({
                                    'x': screen_x,
                                    'y': screen_y,
                                    'area': area,
                                    'distance': distance,
                                    'priority': priority,
                                    'color': color_name
                                })
            
            # ترتيب الأهداف حسب الأولوية
            targets.sort(key=lambda t: t['priority'], reverse=True)
            
            return targets
            
        except Exception as e:
            print(f"❌ خطأ في كشف الأعداء: {e}")
            return []
    
    def predict_target_movement(self, current_target):
        """تنبؤ بحركة الهدف"""
        if self.last_target is None:
            self.last_target = current_target
            return current_target['x'], current_target['y']
        
        # حساب السرعة
        dx = current_target['x'] - self.last_target['x']
        dy = current_target['y'] - self.last_target['y']
        
        # تنبؤ بالموقع التالي
        prediction_factor = self.settings['prediction']
        predicted_x = current_target['x'] + (dx * prediction_factor)
        predicted_y = current_target['y'] + (dy * prediction_factor)
        
        # تطبيق تحيز نحو الرأس (تحريك قليلاً للأعلى)
        predicted_y -= current_target['area'] * self.settings['headshot_bias'] * 0.1
        
        self.last_target = current_target
        return predicted_x, predicted_y
    
    def move_mouse_advanced(self, target_x, target_y, target_info):
        """تحريك الماوس المتقدم"""
        try:
            # الحصول على الموقع الحالي
            current_pos = wintypes.POINT()
            ctypes.windll.user32.GetCursorPos(ctypes.byref(current_pos))
            
            # حساب المسافة
            dx = target_x - current_pos.x
            dy = target_y - current_pos.y
            distance = math.sqrt(dx*dx + dy*dy)
            
            if distance < 3:  # إذا كان قريب جداً
                self.target_locked = True
                return
            
            # حساب قوة الحركة المتكيفة
            distance_factor = min(1.0, distance / 100.0)
            area_factor = min(1.5, target_info['area'] / 100.0)
            
            # تطبيق الإعدادات
            move_strength = self.settings['aim_strength'] * distance_factor
            tracking_strength = self.settings['tracking_speed'] * area_factor
            lock_strength = self.settings['lock_strength'] * (1.0 / max(1.0, distance / 50.0))
            
            # حساب الحركة
            move_x = dx * move_strength * tracking_strength * lock_strength
            move_y = dy * move_strength * tracking_strength * lock_strength
            
            # تطبيق النعومة
            smoothing = self.settings['smoothing']
            move_x *= smoothing
            move_y *= smoothing
            
            # إضافة عشوائية طبيعية
            randomness = 0.3
            move_x += random.uniform(-randomness, randomness)
            move_y += random.uniform(-randomness, randomness)
            
            # تطبيق زمن رد الفعل
            if random.random() < self.settings['reaction_time']:
                move_x *= 0.5
                move_y *= 0.5
            
            # حساب الموقع الجديد
            new_x = current_pos.x + int(move_x)
            new_y = current_pos.y + int(move_y)
            
            # التأكد من الحدود
            new_x = max(0, min(self.screen_width - 1, new_x))
            new_y = max(0, min(self.screen_height - 1, new_y))
            
            # تحريك الماوس
            ctypes.windll.user32.SetCursorPos(new_x, new_y)
            
            self.target_locked = distance < 10
            
        except Exception as e:
            print(f"❌ خطأ في تحريك الماوس: {e}")
    
    def main_loop_advanced(self):
        """الحلقة الرئيسية المتقدمة"""
        print("🚀 بدء التشغيل المتقدم...")
        self.running = True
        
        last_status_time = time.time()
        frame_times = []
        
        while self.running:
            try:
                if self.aimbot_active:
                    frame_start = time.time()
                    
                    # التقاط الشاشة
                    img, offset = self.capture_screen_optimized()
                    
                    if img is not None and offset is not None:
                        # كشف الأعداء
                        targets = self.detect_enemies_advanced(img, offset)
                        
                        if targets:
                            # اختيار أفضل هدف
                            best_target = targets[0]
                            
                            # تنبؤ بالحركة
                            predicted_x, predicted_y = self.predict_target_movement(best_target)
                            
                            # التصويب على الهدف
                            self.move_mouse_advanced(predicted_x, predicted_y, best_target)
                            
                            # تحديث الإحصائيات
                            self.stats['targets_detected'] += 1
                            if self.target_locked:
                                self.stats['successful_locks'] += 1
                        else:
                            self.target_locked = False
                            self.last_target = None
                        
                        # حساب FPS
                        frame_time = time.time() - frame_start
                        frame_times.append(frame_time)
                        if len(frame_times) > 30:
                            frame_times.pop(0)
                        
                        self.stats['frames_processed'] += 1
                        
                        # طباعة الحالة
                        current_time = time.time()
                        if current_time - last_status_time > 3:
                            avg_frame_time = sum(frame_times) / len(frame_times) if frame_times else 0
                            fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0
                            
                            status = "🎯 مُلتصق" if self.target_locked else "🔍 بحث"
                            print(f"{status} | أهداف: {len(targets)} | FPS: {fps:.1f} | قوة: {self.settings['aim_strength']:.1f}")
                            
                            last_status_time = current_time
                
                # تأخير محسن
                time.sleep(0.001)
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ خطأ في الحلقة الرئيسية: {e}")
                time.sleep(0.1)
        
        # طباعة الإحصائيات النهائية
        runtime = time.time() - self.stats['start_time']
        print("\n📊 إحصائيات الأداء:")
        print(f"  وقت التشغيل: {runtime:.1f} ثانية")
        print(f"  الإطارات المعالجة: {self.stats['frames_processed']}")
        print(f"  الأهداف المكتشفة: {self.stats['targets_detected']}")
        print(f"  الالتصاقات الناجحة: {self.stats['successful_locks']}")
        print("⏹️ تم إيقاف النظام المتقدم")
    
    def run(self):
        """تشغيل النظام المتقدم"""
        try:
            self.main_loop_advanced()
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف البرنامج بواسطة المستخدم")
        except Exception as e:
            print(f"❌ خطأ عام: {e}")

def main():
    """الدالة الرئيسية"""
    print("تحقق من المتطلبات المتقدمة...")
    
    # التحقق من المكتبات
    required_libs = ['cv2', 'mss', 'keyboard', 'win32api']
    missing_libs = []
    
    for lib in required_libs:
        try:
            __import__(lib)
        except ImportError:
            missing_libs.append(lib)
    
    if missing_libs:
        print(f"❌ مكتبات مفقودة: {', '.join(missing_libs)}")
        print("قم بتثبيت المكتبات:")
        print("pip install opencv-python mss keyboard pywin32")
        input("اضغط Enter للخروج...")
        return
    
    print("✅ جميع المكتبات متوفرة")
    
    # تشغيل النظام المتقدم
    try:
        aimbot = AdvancedRealAimbot()
        aimbot.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام المتقدم: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
