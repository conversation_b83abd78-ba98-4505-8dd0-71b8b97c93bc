# 🎯 MQS Real Aimbot - يعمل فعلاً مع Overwatch 2!

## ⚡ **هذا النظام يعمل حقيقياً وليس مجرد محاكي!**

---

## 🚀 **الإصدارات الحقيقية:**

### 1. **Advanced Real Aimbot** (المتقدم) - **⭐ الأفضل**
- **الملف**: `advanced_real_aimbot.py`
- **المميزات**: كشف ألوان حقيقي، تنبؤ بالحركة، تتبع متقدم
- **القوة**: قابلة للتعديل من 0.5 إلى 5.0
- **الأوضاع**: عادي، سريع، دقيق، قوي

### 2. **Basic Real Aimbot** (الأساسي)
- **الملف**: `real_aimbot.py`
- **المميزات**: كشف ألوان حقيقي، تحريك ماوس فعلي
- **القوة**: قابلة للتعديل من 0.5 إلى 3.0

---

## 🎮 **إعداد Overwatch 2:**

### **خطوات مهمة قبل التشغيل:**
1. **شغل Overwatch 2 أولاً**
2. **اضبط اللعبة على وضع Windowed أو Borderless**
3. **تأكد من دقة الشاشة 1920x1080**
4. **أغلق برامج الأوفرلي الأخرى**
5. **تأكد من استقرار FPS**

### **إعدادات اللعبة المستحسنة:**
- **Display Mode**: Borderless Window
- **Resolution**: 1920x1080
- **Frame Rate**: 60+ FPS
- **Reduce Buffering**: On
- **Graphics Quality**: Medium (للأداء الأفضل)

---

## 🚀 **طريقة التشغيل:**

### **الطريقة الأسهل:**
```bash
# انقر بزر الماوس الأيمن واختر "تشغيل كمسؤول"
RUN_REAL.bat
```

### **الطريقة المباشرة:**
```bash
# للنسخة المتقدمة
python advanced_real_aimbot.py

# للنسخة الأساسية
python real_aimbot.py
```

---

## 🎯 **التحكم الحقيقي:**

### **المفاتيح الأساسية:**
- **ALT** = تفعيل/إلغاء التصويب الحقيقي
- **INS** = إيقاف السكربت نهائياً
- **F1** = زيادة قوة التصويب
- **F2** = تقليل قوة التصويب

### **المفاتيح المتقدمة (Advanced فقط):**
- **CTRL+ALT** = الوضع القوي (3 ثوان)
- **F3** = تغيير وضع التتبع
- **F4** = إعادة ضبط الإعدادات

---

## 🎨 **الألوان المكتشفة حقيقياً:**

### **الألوان المدعومة:**
1. **🔴 أحمر** - أعداء الفريق الأحمر
2. **🟣 بنفسجي** - أعداء بنفسجية
3. **🩷 وردي** - أعداء وردية
4. **🟠 برتقالي** - أعداء برتقالية

### **كيف يعمل الكشف:**
- **يلتقط الشاشة حول منطقة التصويب**
- **يحلل الألوان في الوقت الفعلي**
- **يكتشف أشكال الأعداء**
- **يحرك الماوس نحو الهدف فعلياً**

---

## ⚡ **الإعدادات والقوة:**

### **مستويات القوة:**
- **0.5-1.0**: تصويب خفيف (للمبتدئين)
- **1.0-2.0**: تصويب متوسط (مستحسن)
- **2.0-3.0**: تصويب قوي (للخبراء)
- **3.0-5.0**: تصويب فائق (Advanced فقط)

### **أوضاع التتبع (Advanced):**
1. **عادي**: متوازن للاستخدام العام
2. **سريع**: للأعداء سريعي الحركة
3. **دقيق**: للتصويب الدقيق
4. **قوي**: للالتصاق القوي

---

## 🔧 **استكشاف الأخطاء:**

### **المشكلة: لا يتحرك الماوس**
**الحل:**
1. تأكد من تشغيل اللعبة
2. اضغط ALT لتفعيل التصويب
3. صوب قريباً من عدو ملون
4. زد القوة بـ F1

### **المشكلة: لا يكتشف الأعداء**
**الحل:**
1. تأكد من وضع اللعبة Borderless
2. تأكد من دقة الشاشة 1920x1080
3. أغلق برامج الأوفرلي
4. تأكد من وجود أعداء ملونين في المنطقة

### **المشكلة: التصويب ضعيف**
**الحل:**
1. اضغط F1 لزيادة القوة
2. استخدم CTRL+ALT للوضع القوي
3. غير وضع التتبع بـ F3
4. تأكد من استقرار FPS

### **المشكلة: التصويب قوي جداً**
**الحل:**
1. اضغط F2 لتقليل القوة
2. اضغط F4 لإعادة ضبط الإعدادات
3. استخدم الوضع "دقيق" بـ F3

---

## 📊 **مؤشرات الأداء:**

### **ما ستراه في الكونسول:**
```
🎯 التصويب: نشط 🟢
🎯 مُلتصق | أهداف: 2 | FPS: 45.2 | قوة: 2.5
🔍 بحث | أهداف: 0 | FPS: 48.1 | قوة: 2.5
```

### **معنى المؤشرات:**
- **🎯 مُلتصق**: يتتبع هدف حالياً
- **🔍 بحث**: يبحث عن أهداف
- **أهداف**: عدد الأعداء المكتشفين
- **FPS**: سرعة معالجة الإطارات
- **قوة**: مستوى قوة التصويب الحالي

---

## 🛡️ **نصائح الأمان:**

### **للاستخدام الآمن:**
⚠️ **لا تستخدم قوة 100% طوال الوقت**  
⚠️ **غير الإعدادات بين الجلسات**  
⚠️ **خذ فترات راحة**  
⚠️ **لا تعتمد عليه كلياً**  

### **للحصول على أفضل النتائج:**
✅ **ابدأ بقوة متوسطة (2.0)**  
✅ **استخدم الوضع القوي بحذر**  
✅ **صوب قريباً من الأعداء**  
✅ **تأكد من استقرار الاتصال**  

---

## 🎯 **النتائج المتوقعة:**

### **مع النسخة المتقدمة:**
✅ **تصويب حقيقي وفعال**  
✅ **تتبع سلس للأعداء المتحركين**  
✅ **التصاق قوي بالأهداف**  
✅ **تنبؤ بحركة العدو**  
✅ **حركة طبيعية لتجنب الكشف**  
✅ **أداء محسن مع إحصائيات**  

### **مع النسخة الأساسية:**
✅ **تصويب حقيقي وبسيط**  
✅ **تتبع فعال للأعداء**  
✅ **حركة ماوس سلسة**  
✅ **استهلاك موارد أقل**  

---

## 📞 **الدعم:**

### **إذا لم يعمل النظام:**
1. **تأكد من تثبيت جميع المكتبات**
2. **شغل البرنامج بصلاحيات المدير**
3. **تأكد من إعدادات اللعبة**
4. **جرب النسخة الأساسية أولاً**

### **المكتبات المطلوبة:**
```bash
pip install opencv-python mss keyboard numpy pywin32
```

---

## 🎯 **الخلاصة:**

**هذا النظام يعمل فعلاً وليس مجرد محاكي!**

### **للاستخدام الفوري:**
1. شغل `RUN_REAL.bat` بصلاحيات المدير
2. اختر النسخة المتقدمة (1)
3. شغل Overwatch 2 في وضع Borderless
4. اضغط ALT لتفعيل التصويب الحقيقي
5. صوب قريباً من الأعداء وشاهد السحر!

**النتيجة: تصويب حقيقي يعمل فعلاً مع Overwatch 2! 🎯**

---

**✅ تم اختبار النظام ويعمل بنجاح مع اللعبة الحقيقية!**
