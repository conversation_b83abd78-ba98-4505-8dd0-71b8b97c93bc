# 🎯 دليل نظام التتبع القوي - MQS Ultra Tracking

## 🔥 المشكلة والحل

### المشكلة السابقة:
- التتبع ضعيف أثناء إطلاق النار
- عدم الالتصاق بالأعداء المتحركين
- كشف ألوان غير دقيق

### الحل الجديد:
✅ **تتبع قوي 600%** - يلتصق بالأعداء بقوة فائقة  
✅ **التصاق مضاعف أثناء الإطلاق** - قوة إضافية عند الضغط على زر الإطلاق  
✅ **72 لون محدد من Overwatch 2** - كشف دقيق للأعداء  
✅ **تنبؤ بحركة العدو** - يتوقع مكان العدو التالي  
✅ **تتبع عدواني** - يطبق تصحيح مضاعف للأعداء السريعين  

---

## ⚙️ الإعدادات الجديدة

### 📁 ملف `config_advanced.py`
يحتوي على جميع الإعدادات المتقدمة:

```python
# إعدادات التتبع القوي
AIM_STRENGTH = 300          # قوة التصويب الأساسية
TRACKING_STRENGTH = 450     # قوة التتبع للأعداء المتحركين  
ULTRA_TRACKING_STRENGTH = 600  # قوة التتبع الفائقة
LOCK_STRENGTH = 400         # قوة الالتصاق بالهدف

# إعدادات متقدمة
AIM_MAX_MOVE_PIXELS = 20    # أقصى حركة في الإطار الواحد
TARGET_COLOR_TOLERANCE = 30  # تحمل كشف الألوان
PREDICTION_ENABLED = True    # تنبؤ بحركة العدو
AGGRESSIVE_TRACKING = True   # التتبع العدواني
```

---

## 🎮 طريقة الاستخدام

### 1. التشغيل السريع
```bash
# انقر بزر الماوس الأيمن واختر "تشغيل كمسؤول"
تشغيل_التتبع_القوي.bat
```

### 2. التحكم في اللعبة
- **ALT** = تفعيل/إلغاء التتبع القوي (Toggle)
- **INS** = إيقاف السكربت نهائياً
- **زر الماوس الأيسر** = تفعيل التتبع المضاعف أثناء الإطلاق

### 3. ضبط القوة
- افتح ملف `config_advanced.py`
- غير القيم حسب حاجتك
- احفظ الملف وأعد تشغيل البرنامج

---

## 🎯 كيف يعمل التتبع القوي

### 1. الكشف المحسن
- يبحث في **72 لون محدد** من Overwatch 2
- يستخدم **HSV color space** للدقة العالية
- يطبق **morphological operations** لتنظيف الكشف

### 2. التتبع المضاعف
```
التتبع العادي = قوة أساسية × 1.5
التتبع أثناء الإطلاق = قوة أساسية × 2.5 × قوة الالتصاق
```

### 3. التنبؤ بالحركة
- يحسب سرعة العدو من الإطارات السابقة
- يتنبأ بموقع العدو في الإطارات القادمة
- يطبق تصحيح استباقي للحركة

### 4. الحركة الطبيعية
- يضيف **عشوائية طبيعية** لتجنب الكشف
- يستخدم **منحنى تسارع** للحركة السلسة
- يحد من **الحركات المشبوهة** الكبيرة

---

## 🔧 ضبط الإعدادات حسب الحاجة

### إذا كان التتبع ضعيف:
```python
ULTRA_TRACKING_STRENGTH = 800  # زيادة القوة
AGGRESSIVE_TRACKING = True     # تفعيل التتبع العدواني
AIM_MAX_MOVE_PIXELS = 25      # زيادة الحركة المسموحة
```

### إذا كان التتبع سريع جداً:
```python
AIM_SPEED = 150               # تقليل السرعة
SMOOTH_TRACKING = True        # تفعيل التتبع السلس
AIM_JITTER_PERCENT = 80       # زيادة العشوائية
```

### إذا لم يكشف الأعداء:
```python
COLOR_DETECTION_STRENGTH = 500  # زيادة قوة الكشف
TARGET_COLOR_TOLERANCE = 40     # زيادة تحمل الألوان
```

---

## 🎨 الألوان المدعومة

### الألوان الأساسية (7):
1. **أحمر** (Red) - أعداء حمر
2. **بنفسجي** (Purple) - أعداء بنفسجية
3. **بنفسجي فاتح** (Violet) - أعداء بنفسجية فاتحة  
4. **وردي** (Pink) - أعداء وردية
5. **برتقالي** (Orange) - أعداء برتقالية
6. **أصفر** (Yellow) - أعداء صفراء
7. **سماوي** (Cyan) - أعداء سماوية

### الألوان المحددة (72):
ألوان دقيقة مستخرجة من لعبة Overwatch 2:
```
d521cd, d722cf, d623ce, d722ce, d621cd, ce19ca, d11ccb, d21dca,
c818cf, d722cd, d722ce, cd19c9, c617d3, cb17c5, da25d3, ce24cc,
... و 56 لون إضافي
```

---

## 📊 مراقبة الأداء

### الإحصائيات المعروضة:
- **عدد الأهداف المكتشفة** لكل لون
- **عدد مرات الالتصاق** الناجحة
- **معدل التتبع** في الثانية
- **دقة التنبؤ** بحركة العدو

### مؤشرات الأداء الجيد:
- كشف أهداف > 10 في الثانية
- معدل التصاق > 80%
- استجابة < 5ms

---

## ⚠️ نصائح مهمة

### للحصول على أفضل أداء:
1. **شغل اللعبة بدقة 1920x1080**
2. **اضبط إعدادات الجرافيك على متوسط**
3. **أغلق البرامج غير الضرورية**
4. **تأكد من استقرار FPS في اللعبة**

### لتجنب الكشف:
1. **لا تستخدم قوة 100% طوال الوقت**
2. **غير الإعدادات بين الجلسات**
3. **استخدم العشوائية الطبيعية**
4. **تجنب الحركات المثالية**

---

## 🐛 حل المشاكل الشائعة

### المشكلة: لا يتتبع الأعداء
**الحل:**
1. تأكد من تشغيل البرنامج بصلاحيات المدير
2. زد `ULTRA_TRACKING_STRENGTH` إلى 800
3. فعل `AGGRESSIVE_TRACKING = True`

### المشكلة: التتبع بطيء
**الحل:**
1. زد `AIM_SPEED` إلى 300
2. زد `AIM_MAX_MOVE_PIXELS` إلى 30
3. قلل `AIM_JITTER_PERCENT` إلى 40

### المشكلة: لا يكشف الألوان
**الحل:**
1. زد `COLOR_DETECTION_STRENGTH` إلى 500
2. زد `TARGET_COLOR_TOLERANCE` إلى 40
3. تأكد من إضاءة الشاشة المناسبة

---

## 📞 الدعم والتحديثات

### الملفات المهمة:
- `mqs_arabic_aimbot.py` - البرنامج الرئيسي
- `config_advanced.py` - الإعدادات المتقدمة
- `run_real_aimbot.py` - ملف التشغيل
- `تشغيل_التتبع_القوي.bat` - تشغيل سريع

### للحصول على أفضل النتائج:
1. اقرأ هذا الدليل كاملاً
2. جرب الإعدادات المختلفة
3. اضبط القوة حسب أسلوب لعبك
4. استخدم التتبع بذكاء

---

**ملاحظة**: هذا النظام مصمم للتدريب والتحسين. استخدمه بمسؤولية! 🎯
