#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQS Real Aimbot for Overwatch 2
نظام التصويب الحقيقي لـ Overwatch 2
"""

import sys
import os
import subprocess
import time
import ctypes
from pathlib import Path

def check_admin():
    """فحص صلاحيات المدير"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """تشغيل البرنامج بصلاحيات المدير"""
    if check_admin():
        return True
    else:
        print("🔐 يتطلب صلاحيات المدير للوصول للعبة...")
        try:
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            return False
        except:
            print("❌ فشل في الحصول على صلاحيات المدير")
            return False

def install_requirements():
    """تثبيت المتطلبات"""
    requirements = [
        'opencv-python',
        'numpy', 
        'mss',
        'pillow',
        'keyboard',
        'mouse'
    ]
    
    print("📦 تثبيت المتطلبات...")
    for req in requirements:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', req])
            print(f"✅ تم تثبيت {req}")
        except:
            print(f"❌ فشل تثبيت {req}")

def check_overwatch_running():
    """فحص ما إذا كانت لعبة Overwatch 2 تعمل"""
    try:
        import psutil
        for proc in psutil.process_iter(['pid', 'name']):
            if 'overwatch' in proc.info['name'].lower():
                return True
        return False
    except:
        return True  # افتراض أن اللعبة تعمل إذا فشل الفحص

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🎯 MQS Real Aimbot for Overwatch 2")
    print("نظام التصويب الحقيقي المحسن")
    print("=" * 60)
    
    # فحص صلاحيات المدير
    if not run_as_admin():
        return
    
    print("✅ تم الحصول على صلاحيات المدير")
    
    # تثبيت المتطلبات
    install_requirements()
    
    # فحص اللعبة
    print("\n🎮 فحص لعبة Overwatch 2...")
    if check_overwatch_running():
        print("✅ تم العثور على Overwatch 2")
    else:
        print("⚠️  لم يتم العثور على Overwatch 2 - تأكد من تشغيل اللعبة")
        input("اضغط Enter للمتابعة...")
    
    # تشغيل البرنامج
    print("\n🚀 تشغيل نظام التصويب الحقيقي...")
    try:
        from mqs_arabic_aimbot import MQSArabicAimBot

        print("✅ تم تحميل النظام بنجاح")
        print("\n📋 تعليمات الاستخدام:")
        print("• ALT = تفعيل/إلغاء تفعيل التصويب")
        print("• INS = إيقاف السكربت")
        print("• اضبط القوة من الواجهة")
        print("• تأكد من أن اللعبة في المقدمة")

        # إنشاء وتشغيل البرنامج
        app = MQSArabicAimBot()
        app.run()
        
    except ImportError as e:
        print(f"❌ خطأ في التحميل: {e}")
        print("تأكد من وجود ملف mqs_arabic_aimbot.py")
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
    
    print("\n👋 تم إنهاء البرنامج")
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
