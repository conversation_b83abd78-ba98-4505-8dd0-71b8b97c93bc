@echo off
title MQS Ultra Tracking Aimbot

echo ================================================================
echo MQS Ultra Tracking Aimbot for Overwatch 2
echo ================================================================
echo.

echo Checking administrator privileges...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Administrator privileges granted
) else (
    echo [ERROR] Administrator privileges required
    echo Right-click this file and select "Run as administrator"
    pause
    exit
)

echo.
echo Checking Python...
python --version >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Python found
) else (
    echo [ERROR] Python not installed
    echo Please install Python from python.org
    pause
    exit
)

echo.
echo Starting Ultra Tracking System...
echo.
echo Controls:
echo - ALT = Toggle Ultra Tracking
echo - INS = Stop script
echo.
echo Ultra Settings Active:
echo - Tracking Power: 600%%
echo - Lock Strength: 400%%
echo - Color Detection: 400%%
echo - Movement Prediction: ON
echo - Aggressive Tracking: ON
echo.

python run_real_aimbot.py

echo.
echo Program finished
pause
