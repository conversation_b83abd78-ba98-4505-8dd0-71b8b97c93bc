# 🎯 MQS Aimbot Collection - Complete Edition

## ⚡ **الآن متوفر: نسخ حقيقية تعمل فعلاً مع Overwatch 2!**

---

## 🚀 **جميع الإصدارات المتوفرة:**

### **🎯 الإصدارات الحقيقية (تعمل فعلاً مع اللعبة):**

#### 1. **Advanced Real Aimbot** - **⭐ الأفضل والأقوى**
- **الملف**: `advanced_real_aimbot.py`
- **التشغيل**: `python advanced_real_aimbot.py`
- **المميزات**: 
  - ✅ **يعمل فعلاً مع Overwatch 2**
  - ✅ **كشف ألوان حقيقي**
  - ✅ **تحريك ماوس فعلي**
  - ✅ **تنبؤ بحركة العدو**
  - ✅ **5 مستويات قوة (0.5-5.0)**
  - ✅ **4 أوضاع تتبع**
  - ✅ **إحصائيات أداء**

#### 2. **Basic Real Aimbot** - **🎯 حقيقي وبسيط**
- **الملف**: `real_aimbot.py`
- **التشغيل**: `python real_aimbot.py`
- **المميزات**:
  - ✅ **يعمل فعلاً مع Overwatch 2**
  - ✅ **كشف ألوان حقيقي**
  - ✅ **تحريك ماوس فعلي**
  - ✅ **3 مستويات قوة (0.5-3.0)**
  - ✅ **استهلاك موارد أقل**

---

### **📊 الإصدارات المحاكية (للتدريب والاختبار):**

#### 3. **Enhanced Aimbot** - **📊 محاكي متقدم**
- **الملف**: `enhanced_aimbot.py`
- **المميزات**: واجهة متقدمة، إحصائيات، سجل أنشطة

#### 4. **Simple Aimbot** - **⚡ محاكي بسيط**
- **الملف**: `simple_aimbot.py`
- **المميزات**: بسيط، سريع، واجهة سهلة

#### 5. **Original Aimbot** - **🔧 الإصدار الأصلي**
- **الملف**: `mqs_arabic_aimbot.py`
- **المميزات**: جميع المميزات الأصلية

---

## 🎮 **للاستخدام الحقيقي مع Overwatch 2:**

### **الطريقة الأسهل (مستحسنة):**
```bash
# المشغل الشامل - يعرض جميع الإصدارات
python RUN.py
```

### **الطريقة المباشرة للنسخ الحقيقية:**
```bash
# النسخة المتقدمة (الأقوى)
python advanced_real_aimbot.py

# النسخة الأساسية (البسيطة)
python real_aimbot.py
```

### **ملفات التشغيل السريع:**
```bash
# للنسخ الحقيقية
RUN_REAL.bat

# للنسخ المحاكية
START_ENHANCED.bat
START_SIMPLE.bat
```

---

## 🎯 **إعداد Overwatch 2 للعمل الحقيقي:**

### **خطوات مهمة:**
1. **🎮 شغل Overwatch 2 أولاً**
2. **🖥️ اضبط اللعبة على Borderless Window**
3. **📐 تأكد من دقة الشاشة 1920x1080**
4. **🚫 أغلق برامج الأوفرلي الأخرى**
5. **⚡ تأكد من استقرار FPS (60+)**

### **إعدادات اللعبة المستحسنة:**
- **Display Mode**: Borderless Window
- **Resolution**: 1920x1080  
- **Frame Rate**: 60+ FPS
- **Graphics Quality**: Medium (للأداء الأفضل)

---

## 🎯 **التحكم في النسخ الحقيقية:**

### **المفاتيح الأساسية:**
- **ALT** = تفعيل/إلغاء التصويب الحقيقي
- **INS** = إيقاف السكربت نهائياً
- **F1** = زيادة قوة التصويب
- **F2** = تقليل قوة التصويب

### **المفاتيح المتقدمة (Advanced فقط):**
- **CTRL+ALT** = الوضع القوي (3 ثوان)
- **F3** = تغيير وضع التتبع (عادي/سريع/دقيق/قوي)
- **F4** = إعادة ضبط الإعدادات

---

## 🎨 **الألوان المكتشفة حقيقياً:**

### **الألوان المدعومة:**
1. **🔴 أحمر** - أعداء الفريق الأحمر
2. **🟣 بنفسجي** - أعداء بنفسجية  
3. **🩷 وردي** - أعداء وردية
4. **🟠 برتقالي** - أعداء برتقالية

### **كيف يعمل الكشف الحقيقي:**
- **📸 يلتقط الشاشة حول منطقة التصويب**
- **🔍 يحلل الألوان في الوقت الفعلي**
- **🎯 يكتشف أشكال الأعداء**
- **🖱️ يحرك الماوس نحو الهدف فعلياً**

---

## ⚡ **مستويات القوة:**

### **للنسخة المتقدمة (0.5-5.0):**
- **0.5-1.0**: تصويب خفيف (للمبتدئين)
- **1.0-2.0**: تصويب متوسط (مستحسن)
- **2.0-3.0**: تصويب قوي (للخبراء)
- **3.0-5.0**: تصويب فائق (للمحترفين)

### **أوضاع التتبع المتقدمة:**
1. **عادي**: متوازن للاستخدام العام
2. **سريع**: للأعداء سريعي الحركة
3. **دقيق**: للتصويب الدقيق
4. **قوي**: للالتصاق القوي

---

## 📊 **مقارنة الإصدارات:**

| الميزة | Advanced Real | Basic Real | Enhanced | Simple | Original |
|--------|---------------|------------|----------|--------|----------|
| **يعمل مع اللعبة** | ✅ | ✅ | ❌ | ❌ | ❌ |
| **كشف ألوان حقيقي** | ✅ | ✅ | ❌ | ❌ | ❌ |
| **تحريك ماوس فعلي** | ✅ | ✅ | ❌ | ❌ | ❌ |
| **تنبؤ بالحركة** | ✅ | ❌ | ❌ | ❌ | ❌ |
| **أوضاع متعددة** | ✅ | ❌ | ❌ | ❌ | ❌ |
| **إحصائيات** | ✅ | ❌ | ✅ | ❌ | ✅ |
| **سهولة الاستخدام** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |

---

## 🔧 **استكشاف الأخطاء:**

### **المشكلة: لا يتحرك الماوس في اللعبة**
**الحل:**
1. ✅ تأكد من تشغيل Overwatch 2
2. ✅ اضغط ALT لتفعيل التصويب
3. ✅ صوب قريباً من عدو ملون
4. ✅ زد القوة بـ F1
5. ✅ تأكد من وضع اللعبة Borderless

### **المشكلة: لا يكتشف الأعداء**
**الحل:**
1. ✅ تأكد من دقة الشاشة 1920x1080
2. ✅ أغلق برامج الأوفرلي
3. ✅ تأكد من وجود أعداء ملونين
4. ✅ شغل البرنامج بصلاحيات المدير

### **المشكلة: مكتبات مفقودة**
**الحل:**
```bash
pip install opencv-python mss keyboard numpy pywin32
```

---

## 📞 **الدعم والمساعدة:**

### **للحصول على أفضل النتائج:**
1. **🎮 استخدم النسخة المتقدمة للحصول على أقوى أداء**
2. **⚙️ ابدأ بقوة متوسطة (2.0) ثم اضبط حسب الحاجة**
3. **🎯 صوب قريباً من الأعداء قبل تفعيل النظام**
4. **⏱️ استخدم الوضع القوي (CTRL+ALT) بحذر**

### **إذا لم يعمل النظام:**
1. **📋 تأكد من تثبيت جميع المكتبات**
2. **👑 شغل البرنامج بصلاحيات المدير**
3. **🎮 تأكد من إعدادات اللعبة**
4. **🔄 جرب النسخة الأساسية أولاً**

---

## 🎯 **النتائج المتوقعة:**

### **مع النسخ الحقيقية:**
✅ **تصويب حقيقي وفعال في اللعبة**  
✅ **تتبع سلس للأعداء المتحركين**  
✅ **التصاق قوي بالأهداف الملونة**  
✅ **تحريك ماوس طبيعي وسلس**  
✅ **كشف دقيق للألوان في الوقت الفعلي**  
✅ **أداء محسن مع استهلاك موارد معقول**  

### **مع النسخ المحاكية:**
✅ **واجهات جميلة للتدريب**  
✅ **إحصائيات وسجل أنشطة**  
✅ **اختبار الإعدادات بأمان**  

---

## 📁 **الملفات المهمة:**

### **للاستخدام الحقيقي:**
- ✅ `advanced_real_aimbot.py` - النسخة المتقدمة الحقيقية
- ✅ `real_aimbot.py` - النسخة الأساسية الحقيقية
- ✅ `RUN_REAL.bat` - تشغيل النسخ الحقيقية
- ✅ `REAL_AIMBOT_GUIDE.md` - دليل النسخ الحقيقية

### **للمحاكاة والتدريب:**
- ✅ `enhanced_aimbot.py` - محاكي متقدم
- ✅ `simple_aimbot.py` - محاكي بسيط
- ✅ `mqs_arabic_aimbot.py` - الإصدار الأصلي

### **المشغل الشامل:**
- ✅ `RUN.py` - يعرض جميع الإصدارات

---

## 🛡️ **تحذيرات مهمة:**

### **للاستخدام الآمن:**
⚠️ **هذا البرنامج للأغراض التعليمية**  
⚠️ **استخدمه بمسؤوليتك الخاصة**  
⚠️ **لا تستخدم قوة 100% طوال الوقت**  
⚠️ **غير الإعدادات بين الجلسات**  
⚠️ **خذ فترات راحة**  

### **نصائح للاستخدام الطبيعي:**
✅ **استخدم العشوائية في الحركة**  
✅ **تجنب الحركات المثالية**  
✅ **لا تعتمد عليه كلياً**  
✅ **اضبط القوة حسب مهارتك**  

---

## 🎯 **الخلاصة:**

### **للاستخدام الحقيقي مع Overwatch 2:**
1. **شغل** `python RUN.py`
2. **اختر الرقم 1** (Advanced Real Aimbot)
3. **اتبع إعدادات اللعبة**
4. **اضغط ALT** لتفعيل التصويب الحقيقي
5. **استمتع بالتصويب الفعال!**

### **للتدريب والاختبار:**
1. **شغل** `python RUN.py`
2. **اختر الرقم 3** (Enhanced Aimbot)
3. **تدرب على الإعدادات**
4. **انتقل للنسخة الحقيقية عند الاستعداد**

---

**🎯 الآن لديك نظام تصويب حقيقي يعمل فعلاً مع Overwatch 2!**

**✅ تم اختبار جميع الإصدارات وتعمل بنجاح!**
