# 🎯 MQS Ultra Tracking - Quick Start

## 🚀 How to Run (3 Ways)

### Method 1: Simple Python (Recommended)
```bash
# Right-click and "Run as administrator"
python START.py
```

### Method 2: Batch File
```bash
# Right-click and "Run as administrator"  
RUN.bat
```

### Method 3: Advanced Launcher
```bash
# Right-click and "Run as administrator"
python run_real_aimbot.py
```

## ⚙️ Ultra Tracking Settings

### Current Power Levels:
- **Tracking Power**: 600% (6x normal)
- **Lock Strength**: 400% (4x normal)  
- **Color Detection**: 400% (4x normal)
- **Movement Prediction**: Active
- **Aggressive Tracking**: Active

### Controls:
- **ALT** = Toggle Ultra Tracking ON/OFF
- **INS** = Stop Script Completely

## 🎮 For Overwatch 2

### Before Starting:
1. Launch Overwatch 2 first
2. Set game to Windowed or Borderless
3. Make sure game resolution is 1920x1080
4. Close other overlay programs

### During Use:
1. Press ALT to activate ultra tracking
2. Aim near enemies (red, purple, pink, etc.)
3. The system will lock onto targets automatically
4. Extra strong tracking when shooting

## 🔧 Customize Settings

Edit `config_advanced.py` to change:
```python
# Make tracking even stronger
ULTRA_TRACKING_STRENGTH = 800  # Default: 600

# Make lock more aggressive  
LOCK_STRENGTH = 500           # Default: 400

# Increase color detection
COLOR_DETECTION_STRENGTH = 500 # Default: 400
```

## 🐛 Troubleshooting

### Problem: "Administrator privileges required"
**Solution**: Right-click the file and select "Run as administrator"

### Problem: "Python not found"  
**Solution**: Install Python from python.org

### Problem: No tracking/aiming
**Solution**: 
1. Make sure Overwatch 2 is running
2. Press ALT to activate
3. Increase tracking power in config_advanced.py

### Problem: Tracking too weak
**Solution**: Edit config_advanced.py:
```python
ULTRA_TRACKING_STRENGTH = 1000
AGGRESSIVE_TRACKING = True
AIM_MAX_MOVE_PIXELS = 30
```

### Problem: Tracking too fast
**Solution**: Edit config_advanced.py:
```python
AIM_SPEED = 150
SMOOTH_TRACKING = True
AIM_JITTER_PERCENT = 80
```

## 📊 What's New in Ultra Tracking

### Enhanced Features:
✅ **600% Tracking Power** - Much stronger than before  
✅ **Double Lock During Shooting** - Extra strong when firing  
✅ **72 Specific Colors** - Precise Overwatch 2 enemy detection  
✅ **Movement Prediction** - Predicts where enemies will move  
✅ **Aggressive Tracking** - Double correction for fast enemies  
✅ **Natural Movement** - Smooth curves to avoid detection  

### Technical Improvements:
- Real screen capture with MSS
- HSV color space for better detection  
- Morphological operations for clean detection
- Multi-step smooth mouse movement
- Velocity-based enemy prediction
- Adaptive tracking strength

## 🎯 Expected Results

With Ultra Tracking you should see:
- **Strong lock** onto colored enemies
- **Sticky aim** that follows moving targets
- **Extra strong tracking** when shooting
- **Smooth natural movement**
- **High detection rate** for enemy colors

## ⚠️ Important Notes

1. **Use Responsibly** - This is for training/improvement
2. **Administrator Required** - Needed for game access
3. **Overwatch 2 Only** - Designed specifically for OW2
4. **1920x1080 Recommended** - Best performance at this resolution

---

**Quick Start**: Right-click `START.py` → "Run as administrator" → Press ALT in game! 🎯
