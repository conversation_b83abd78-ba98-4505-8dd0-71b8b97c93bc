@echo off
title MQS Real Aimbot

echo ================================================================
echo MQS Real Aimbot for Overwatch 2
echo ================================================================
echo.

echo Checking administrator privileges...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Administrator privileges granted
) else (
    echo [ERROR] Administrator privileges required
    echo Right-click this file and select "Run as administrator"
    pause
    exit
)

echo.
echo Checking Python...
python --version >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Python found
) else (
    echo [ERROR] Python not installed
    echo Please install Python from python.org
    pause
    exit
)

echo.
echo Starting Real Aimbot System...
echo.
echo Quick Instructions:
echo - ALT = Toggle Aimbot
echo - INS = Stop Script
echo - Make sure Overwatch 2 is running first
echo.

python run_real_aimbot.py

echo.
echo Program finished
pause
