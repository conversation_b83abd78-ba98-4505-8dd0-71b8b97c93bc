# Overwatch 2 Real Aimbot

## سكريبت حقيقي يعمل داخل لعبة Overwatch 2

### المميزات:
- **تتبع حقيقي للأعداء** - يتبع الأعداء أثناء الحركة
- **كشف الألوان المتقدم** - يكشف الأحمر، البنفسجي، الوردي، البرتقالي
- **تحكم في الارتداد** - يقلل ارتداد السلاح
- **حركة طبيعية** - تبدو كحركة لاعب حقيقي
- **سرعة عالية** - تحديث كل 10ms

### طريقة التشغيل:

1. **تشغيل البرنامج:**
   ```
   double-click على run_aimbot.bat
   ```

2. **أزرار التحكم:**
   - `ALT` = تفعيل/إيقاف Aim Assist
   - `CTRL` = تفعيل/إيقاف Recoil Control  
   - `INSERT` = إيقاف البرنامج نهائياً

### كيفية الاستخدام في اللعبة:

1. شغل Overwatch 2
2. شغل البرنامج بـ run_aimbot.bat
3. اضغط ALT لتفعيل Aim Assist
4. اضغط CTRL لتفعيل Recoil Control
5. ابدأ اللعب - السكريبت سيتبع الأعداء تلقائياً

### الإعدادات المتقدمة:

- **قوة التتبع:** 95% (عالية جداً)
- **سرعة التتبع:** 80% (سريعة)
- **نطاق البحث:** 200 بكسل حول المؤشر
- **ألوان الكشف:** أحمر، بنفسجي، وردي، برتقالي

### ملاحظات مهمة:

- يعمل فقط مع Overwatch 2
- يتطلب تشغيل البرنامج كمدير (Administrator)
- تأكد من أن اللعبة في وضع Windowed أو Borderless
- لا تحرك الماوس بقوة أثناء التتبع

### استكشاف الأخطاء:

إذا لم يعمل السكريبت:
1. تأكد من تشغيله كمدير
2. تأكد من أن Python مثبت
3. تأكد من أن جميع المكتبات مثبتة
4. تأكد من أن اللعبة مفتوحة

---
**تحذير:** استخدم هذا البرنامج على مسؤوليتك الخاصة
